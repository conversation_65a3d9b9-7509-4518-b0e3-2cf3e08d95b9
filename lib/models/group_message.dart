/// 群聊消息模型
class GroupMessage {
  /// 消息ID
  final String id;

  /// 群聊ID
  final String groupId;

  /// 发送者ID
  final String senderId;

  /// 发送者用户名 (从关联的用户数据获取)
  final String? senderName;

  /// 发送者头像 (从关联的用户数据获取)
  final String? senderAvatar;

  /// 消息内容
  final String content;

  /// 消息类型
  final GroupMessageType messageType;

  /// 消息状态
  final String status;

  /// 创建时间
  final DateTime createdAt;

  /// 是否为系统消息
  final bool isSystemMessage;

  /// 回复的消息ID (可选)
  final String? replyToMessageId;

  /// 提及的用户ID列表 (可选)
  final List<String> mentionedUsers;

  GroupMessage({
    required this.id,
    required this.groupId,
    required this.senderId,
    this.senderName,
    this.senderAvatar,
    required this.content,
    this.messageType = GroupMessageType.text,
    this.status = 'sent',
    required this.createdAt,
    this.isSystemMessage = false,
    this.replyToMessageId,
    this.mentionedUsers = const [],
  });

  /// 从JSON创建群聊消息对象
  factory GroupMessage.fromJson(Map<String, dynamic> json) {
    return GroupMessage(
      id: json['id'] ?? '',
      groupId: json['group_id'] ?? '',
      senderId: json['sender_id'] ?? json['expand']?['sender']?['id'] ?? '',
      senderName: json['expand']?['sender']?['username'],
      senderAvatar: json['expand']?['sender']?['avatar'],
      content: json['content'] ?? '',
      messageType: GroupMessageTypeExtension.fromString(
        json['message_type'] ?? 'text',
      ),
      status: json['status'] ?? 'sent',
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : json['created'] != null
              ? DateTime.parse(json['created'])
              : DateTime.now(),
      isSystemMessage: json['is_system_message'] ?? false,
      replyToMessageId: json['reply_to_message_id'],
      mentionedUsers:
          json['mentioned_users'] != null
              ? List<String>.from(json['mentioned_users'])
              : [],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'group_id': groupId,
      'sender_id': senderId,
      'content': content,
      'message_type': messageType.name,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'is_system_message': isSystemMessage,
      'reply_to_message_id': replyToMessageId,
      'mentioned_users': mentionedUsers,
    };
  }

  /// 是否是当前用户发送的消息
  bool isSentByUser(String userId) {
    return senderId == userId;
  }

  /// 获取显示的发送者名称
  String get displaySenderName {
    if (isSystemMessage) return '系统';
    return senderName ?? '未知用户';
  }

  /// 获取显示的消息内容
  String get displayContent {
    switch (messageType) {
      case GroupMessageType.text:
        return content;
      case GroupMessageType.image:
        return '[图片]';
      case GroupMessageType.system:
        return content;
      case GroupMessageType.join:
        return '$displaySenderName 加入了群聊';
      case GroupMessageType.leave:
        return '$displaySenderName 离开了群聊';
    }
  }

  /// 复制并更新消息信息
  GroupMessage copyWith({
    String? id,
    String? groupId,
    String? senderId,
    String? senderName,
    String? senderAvatar,
    String? content,
    GroupMessageType? messageType,
    String? status,
    DateTime? createdAt,
    bool? isSystemMessage,
    String? replyToMessageId,
    List<String>? mentionedUsers,
  }) {
    return GroupMessage(
      id: id ?? this.id,
      groupId: groupId ?? this.groupId,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      senderAvatar: senderAvatar ?? this.senderAvatar,
      content: content ?? this.content,
      messageType: messageType ?? this.messageType,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      isSystemMessage: isSystemMessage ?? this.isSystemMessage,
      replyToMessageId: replyToMessageId ?? this.replyToMessageId,
      mentionedUsers: mentionedUsers ?? this.mentionedUsers,
    );
  }

  @override
  String toString() {
    return 'GroupMessage(id: $id, groupId: $groupId, senderId: $senderId, content: $content)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GroupMessage && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// 群聊消息类型枚举
enum GroupMessageType {
  text, // 文本消息
  image, // 图片消息
  system, // 系统消息
  join, // 加入群聊
  leave, // 离开群聊
}

extension GroupMessageTypeExtension on GroupMessageType {
  /// 从字符串创建枚举值
  static GroupMessageType fromString(String type) {
    switch (type.toLowerCase()) {
      case 'text':
        return GroupMessageType.text;
      case 'image':
        return GroupMessageType.image;
      case 'system':
        return GroupMessageType.system;
      case 'join':
        return GroupMessageType.join;
      case 'leave':
        return GroupMessageType.leave;
      default:
        return GroupMessageType.text;
    }
  }

  String get name {
    switch (this) {
      case GroupMessageType.text:
        return 'text';
      case GroupMessageType.image:
        return 'image';
      case GroupMessageType.system:
        return 'system';
      case GroupMessageType.join:
        return 'join';
      case GroupMessageType.leave:
        return 'leave';
    }
  }

  String get displayName {
    switch (this) {
      case GroupMessageType.text:
        return '文本';
      case GroupMessageType.image:
        return '图片';
      case GroupMessageType.system:
        return '系统';
      case GroupMessageType.join:
        return '加入';
      case GroupMessageType.leave:
        return '离开';
    }
  }
}
