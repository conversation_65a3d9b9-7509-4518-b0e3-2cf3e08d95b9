import 'package:latlong2/latlong.dart';

/// 钓鱼活动模型类
///
/// 遵循 PocketBase 标准字段命名规范：
/// - 使用 PocketBase 内置的 id, created, updated 字段
/// - 地理位置使用单独的 latitude, longitude 字段
/// - 关系数据通过单独的集合管理
class FishingActivity {
  /// 唯一标识符 (PocketBase 自动生成)
  final String id;

  /// 活动标题 (必填)
  final String title;

  /// 活动描述 (可选)
  final String description;

  /// 活动地点 (geoPoint字段)
  final Map<String, dynamic>? location;

  /// 地点名称 (可选)
  final String? locationName;

  /// 开始时间
  final DateTime startTime;

  /// 结束时间
  final DateTime endTime;

  /// 最大参与人数 (默认999，实际无限制)
  final int maxParticipants;

  /// 当前参与人数
  final int currentParticipants;

  /// 创建者用户ID (关联到 users 集合)
  final String creatorId;

  /// 创建者用户名 (从关联的用户数据获取)
  final String? creatorName;

  /// 创建者头像 (从关联的用户数据获取)
  final String? creatorAvatar;

  /// 活动状态
  final ActivityStatus status;

  /// 活动图片列表
  final List<String> images;

  /// 创建时间 (PocketBase 自动管理)
  final DateTime created;

  /// 更新时间 (PocketBase 自动管理)
  final DateTime updated;

  /// 群聊ID (关联到群聊)
  final String? groupChatId;

  FishingActivity({
    required this.id,
    required this.title,
    this.description = '',
    this.location,
    this.locationName,
    required this.startTime,
    required this.endTime,
    this.maxParticipants = 999,
    this.currentParticipants = 0,
    required this.creatorId,
    this.creatorName,
    this.creatorAvatar,
    this.status = ActivityStatus.active,
    this.images = const [],
    required this.created,
    required this.updated,
    this.groupChatId,
  });

  /// 从JSON创建活动对象
  factory FishingActivity.fromJson(Map<String, dynamic> json) {
    return FishingActivity(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      location: json['location'],
      locationName: json['location_name'],
      startTime: DateTime.parse(json['start_time']),
      endTime: DateTime.parse(json['end_time']),
      maxParticipants: json['max_participants'] ?? 999,
      currentParticipants: json['current_participants'] ?? 0,
      creatorId: json['creator_id'] ?? json['expand']?['creator']?['id'] ?? '',
      creatorName: json['expand']?['creator']?['username'],
      creatorAvatar: json['expand']?['creator']?['avatar'],
      status: ActivityStatusExtension.fromString(json['status'] ?? 'active'),
      images: json['images'] != null ? List<String>.from(json['images']) : [],
      created: DateTime.parse(json['created']),
      updated: DateTime.parse(json['updated']),
      groupChatId: json['group_chat_id'],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'location': location,
      'location_name': locationName,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime.toIso8601String(),
      'max_participants': maxParticipants,
      'current_participants': currentParticipants,
      'creator_id': creatorId,
      'status': status.name,
      'images': images,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
      'group_chat_id': groupChatId,
    };
  }

  /// 获取活动位置的经纬度
  Map<String, double>? get locationLatLng {
    if (location == null) return null;
    return {
      'lat': location!['lat']?.toDouble() ?? 0.0,
      'lng': location!['lng']?.toDouble() ?? 0.0,
    };
  }

  /// 获取活动位置的LatLng对象
  LatLng? get locationAsLatLng {
    if (location == null) return null;
    return LatLng(
      location!['lat']?.toDouble() ?? 0.0,
      location!['lng']?.toDouble() ?? 0.0,
    );
  }

  /// 检查活动是否已满员
  bool get isFull => currentParticipants >= maxParticipants;

  /// 检查活动是否已开始
  bool get isStarted => DateTime.now().isAfter(startTime);

  /// 检查活动是否已结束
  bool get isEnded => DateTime.now().isAfter(endTime);

  /// 获取活动状态显示文本
  String get statusDisplayText {
    switch (status) {
      case ActivityStatus.active:
        if (isEnded) return '已结束';
        if (isStarted) return '进行中';
        if (isFull) return '已满员';
        return '报名中';
      case ActivityStatus.cancelled:
        return '已取消';
      case ActivityStatus.completed:
        return '已完成';
    }
  }

  /// 获取主要显示图片
  String? get primaryImage {
    if (images.isNotEmpty) {
      return images.first;
    }
    return creatorAvatar;
  }

  /// 复制并更新活动信息
  FishingActivity copyWith({
    String? id,
    String? title,
    String? description,
    Map<String, dynamic>? location,
    String? locationName,
    DateTime? startTime,
    DateTime? endTime,
    int? maxParticipants,
    int? currentParticipants,
    String? creatorId,
    String? creatorName,
    String? creatorAvatar,
    ActivityStatus? status,
    List<String>? images,
    DateTime? created,
    DateTime? updated,
    String? groupChatId,
  }) {
    return FishingActivity(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      location: location ?? this.location,
      locationName: locationName ?? this.locationName,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      maxParticipants: maxParticipants ?? this.maxParticipants,
      currentParticipants: currentParticipants ?? this.currentParticipants,
      creatorId: creatorId ?? this.creatorId,
      creatorName: creatorName ?? this.creatorName,
      creatorAvatar: creatorAvatar ?? this.creatorAvatar,
      status: status ?? this.status,
      images: images ?? this.images,
      created: created ?? this.created,
      updated: updated ?? this.updated,
      groupChatId: groupChatId ?? this.groupChatId,
    );
  }

  @override
  String toString() {
    return 'FishingActivity(id: $id, title: $title, creatorId: $creatorId, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FishingActivity && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// 活动状态枚举
enum ActivityStatus {
  active, // 活跃状态
  cancelled, // 已取消
  completed, // 已完成
}

extension ActivityStatusExtension on ActivityStatus {
  /// 从字符串创建枚举值
  static ActivityStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return ActivityStatus.active;
      case 'cancelled':
        return ActivityStatus.cancelled;
      case 'completed':
        return ActivityStatus.completed;
      default:
        return ActivityStatus.active;
    }
  }

  String get name {
    switch (this) {
      case ActivityStatus.active:
        return 'active';
      case ActivityStatus.cancelled:
        return 'cancelled';
      case ActivityStatus.completed:
        return 'completed';
    }
  }

  String get displayName {
    switch (this) {
      case ActivityStatus.active:
        return '活跃';
      case ActivityStatus.cancelled:
        return '已取消';
      case ActivityStatus.completed:
        return '已完成';
    }
  }
}
