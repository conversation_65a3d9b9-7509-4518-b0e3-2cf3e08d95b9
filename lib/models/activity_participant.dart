/// 活动参与者关系模型
///
/// 用于管理用户对活动的参与关系
/// 遵循 PocketBase 关系型数据库设计原则
class ActivityParticipant {
  /// 唯一标识符 (PocketBase 自动生成)
  final String id;

  /// 活动ID
  final String activityId;

  /// 参与者用户ID
  final String userId;

  /// 参与者用户名 (从关联的用户数据获取)
  final String? userName;

  /// 参与者头像 (从关联的用户数据获取)
  final String? userAvatar;

  /// 参与时间
  final DateTime joinTime;

  /// 参与状态
  final ParticipantStatus status;

  /// 创建时间 (PocketBase 自动管理)
  final DateTime created;

  /// 更新时间 (PocketBase 自动管理)
  final DateTime updated;

  ActivityParticipant({
    required this.id,
    required this.activityId,
    required this.userId,
    this.userName,
    this.userAvatar,
    required this.joinTime,
    this.status = ParticipantStatus.joined,
    required this.created,
    required this.updated,
  });

  /// 从JSON创建参与者对象
  factory ActivityParticipant.fromJson(Map<String, dynamic> json) {
    return ActivityParticipant(
      id: json['id'] ?? '',
      activityId: json['activity_id'] ?? '',
      userId: json['user_id'] ?? json['expand']?['user']?['id'] ?? '',
      userName: json['expand']?['user']?['username'],
      userAvatar: json['expand']?['user']?['avatar'],
      joinTime: DateTime.parse(json['join_time']),
      status: ParticipantStatusExtension.fromString(json['status'] ?? 'joined'),
      created: DateTime.parse(json['created']),
      updated: DateTime.parse(json['updated']),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'activity_id': activityId,
      'user_id': userId,
      'join_time': joinTime.toIso8601String(),
      'status': status.name,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }

  /// 复制并更新参与者信息
  ActivityParticipant copyWith({
    String? id,
    String? activityId,
    String? userId,
    String? userName,
    String? userAvatar,
    DateTime? joinTime,
    ParticipantStatus? status,
    DateTime? created,
    DateTime? updated,
  }) {
    return ActivityParticipant(
      id: id ?? this.id,
      activityId: activityId ?? this.activityId,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userAvatar: userAvatar ?? this.userAvatar,
      joinTime: joinTime ?? this.joinTime,
      status: status ?? this.status,
      created: created ?? this.created,
      updated: updated ?? this.updated,
    );
  }

  @override
  String toString() {
    return 'ActivityParticipant(id: $id, activityId: $activityId, userId: $userId, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ActivityParticipant && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// 参与者状态枚举
enum ParticipantStatus {
  joined, // 已参与
  left, // 已退出
  kicked, // 被踢出
}

extension ParticipantStatusExtension on ParticipantStatus {
  /// 从字符串创建枚举值
  static ParticipantStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'joined':
        return ParticipantStatus.joined;
      case 'left':
        return ParticipantStatus.left;
      case 'kicked':
        return ParticipantStatus.kicked;
      default:
        return ParticipantStatus.joined;
    }
  }

  String get name {
    switch (this) {
      case ParticipantStatus.joined:
        return 'joined';
      case ParticipantStatus.left:
        return 'left';
      case ParticipantStatus.kicked:
        return 'kicked';
    }
  }

  String get displayName {
    switch (this) {
      case ParticipantStatus.joined:
        return '已参与';
      case ParticipantStatus.left:
        return '已退出';
      case ParticipantStatus.kicked:
        return '被踢出';
    }
  }
}
