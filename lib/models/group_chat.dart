/// 群聊模型
///
/// 用于管理活动群聊信息
class GroupChat {
  /// 唯一标识符 (PocketBase 自动生成)
  final String id;

  /// 关联的活动ID
  final String activityId;

  /// 群聊名称
  final String groupName;

  /// 群聊描述
  final String? description;

  /// 群聊头像
  final String? avatar;

  /// 群聊创建者ID
  final String creatorId;

  /// 群聊状态
  final GroupChatStatus status;

  /// 成员数量
  final int memberCount;

  /// 最后一条消息内容
  final String? lastMessage;

  /// 最后一条消息时间
  final DateTime? lastMessageTime;

  /// 创建时间 (PocketBase 自动管理)
  final DateTime created;

  /// 更新时间 (PocketBase 自动管理)
  final DateTime updated;

  GroupChat({
    required this.id,
    required this.activityId,
    required this.groupName,
    this.description,
    this.avatar,
    required this.creatorId,
    this.status = GroupChatStatus.active,
    this.memberCount = 0,
    this.lastMessage,
    this.lastMessageTime,
    required this.created,
    required this.updated,
  });

  /// 从JSON创建群聊对象
  factory GroupChat.fromJson(Map<String, dynamic> json) {
    return GroupChat(
      id: json['id'] ?? '',
      activityId: json['activity_id'] ?? '',
      groupName: json['group_name'] ?? '',
      description: json['description'],
      avatar: json['avatar'],
      creatorId: json['creator_id'] ?? '',
      status: GroupChatStatusExtension.fromString(json['status'] ?? 'active'),
      memberCount: json['member_count'] ?? 0,
      lastMessage: json['last_message'],
      lastMessageTime:
          json['last_message_time'] != null
              ? DateTime.parse(json['last_message_time'])
              : null,
      created: DateTime.parse(json['created']),
      updated: DateTime.parse(json['updated']),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'activity_id': activityId,
      'group_name': groupName,
      'description': description,
      'avatar': avatar,
      'creator_id': creatorId,
      'status': status.name,
      'member_count': memberCount,
      'last_message': lastMessage,
      'last_message_time': lastMessageTime?.toIso8601String(),
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }

  /// 复制并更新群聊信息
  GroupChat copyWith({
    String? id,
    String? activityId,
    String? groupName,
    String? description,
    String? avatar,
    String? creatorId,
    GroupChatStatus? status,
    int? memberCount,
    String? lastMessage,
    DateTime? lastMessageTime,
    DateTime? created,
    DateTime? updated,
  }) {
    return GroupChat(
      id: id ?? this.id,
      activityId: activityId ?? this.activityId,
      groupName: groupName ?? this.groupName,
      description: description ?? this.description,
      avatar: avatar ?? this.avatar,
      creatorId: creatorId ?? this.creatorId,
      status: status ?? this.status,
      memberCount: memberCount ?? this.memberCount,
      lastMessage: lastMessage ?? this.lastMessage,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
      created: created ?? this.created,
      updated: updated ?? this.updated,
    );
  }

  @override
  String toString() {
    return 'GroupChat(id: $id, activityId: $activityId, groupName: $groupName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GroupChat && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// 群聊状态枚举
enum GroupChatStatus {
  active, // 活跃状态
  archived, // 已归档
  deleted, // 已删除
}

extension GroupChatStatusExtension on GroupChatStatus {
  /// 从字符串创建枚举值
  static GroupChatStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return GroupChatStatus.active;
      case 'archived':
        return GroupChatStatus.archived;
      case 'deleted':
        return GroupChatStatus.deleted;
      default:
        return GroupChatStatus.active;
    }
  }

  String get name {
    switch (this) {
      case GroupChatStatus.active:
        return 'active';
      case GroupChatStatus.archived:
        return 'archived';
      case GroupChatStatus.deleted:
        return 'deleted';
    }
  }

  String get displayName {
    switch (this) {
      case GroupChatStatus.active:
        return '活跃';
      case GroupChatStatus.archived:
        return '已归档';
      case GroupChatStatus.deleted:
        return '已删除';
    }
  }
}
