import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:flutter_map_location_marker/flutter_map_location_marker.dart';
import 'package:latlong2/latlong.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:async';
import 'dart:math' as math;
import '../services/service_locator.dart';
import '../models/fishing_spot.dart';
import '../models/fishing_activity.dart';
import 'activity_detail_page.dart';
import '../utils/map_coordinate_utils.dart';
import '../utils/marker_alignment_utils.dart';
import '../utils/tianditu_utils.dart';

import '../widgets/dev_menu.dart';
import '../widgets/split_screen_add_spot.dart';
import '../widgets/split_screen_activity_create.dart';
import '../widgets/fishing_spot_marker.dart';
import '../widgets/photo_fishing_spot_marker.dart';
import 'spot_detail_page.dart';
import '../widgets/optimized_search_bar.dart';
import '../widgets/snackbar.dart';

class HomePage extends StatefulWidget {
  final Function(bool)? onBottomNavigationBarVisibilityChanged;

  const HomePage({super.key, this.onBottomNavigationBarVisibilityChanged});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with TickerProviderStateMixin {
  // 天地图密钥，需要在天地图官网申请
  final MapController mapController = MapController();
  // 地图类型：true为矢量图，false为卫星图
  bool isVectorMap = false;
  // 是否显示注记层
  bool showAnnotationLayer = true;

  // 服务 - 使用新的服务架构
  // 通过Services便捷访问器访问服务

  // 数据
  final List<FishingSpot> _spots = [];
  final List<FishingActivity> _activities = [];
  LatLng _userLocation = const LatLng(39.9042, 116.4074); // 默认北京位置
  bool _isLoading = true;

  final GlobalKey _mapKey = GlobalKey();

  // 分屏模式状态
  bool _isSplitScreenMode = false;
  bool _isActivityCreateMode = false; // 是否为创建活动模式
  LatLng _centerMarkerLocation = const LatLng(39.9042, 116.4074);
  String? _suggestedSpotName; // 建议的钓点名称
  String? _suggestedActivityTitle; // 建议的活动标题

  // 保存进入分屏模式前的地图中心位置，用于退出时恢复
  LatLng? _originalMapCenter;

  // 动画控制器（保留用于未来扩展）
  late AnimationController _animationController;

  // 定时位置更新
  Timer? _locationUpdateTimer;
  final bool _enablePeriodicLocationUpdate = true;
  static const Duration _locationUpdateInterval = Duration(seconds: 10);

  // 位置重置按钮状态
  bool _isLocationResetting = false;

  // 位置监听订阅
  StreamSubscription<LatLng>? _locationSubscription;

  // 搜索栏状态
  bool _isSearching = false;

  // 标记显示模式：true为照片模式，false为emoji模式
  bool _usePhotoMarkers = true;

  @override
  void initState() {
    super.initState();

    // 调试：检查回调是否正确传递
    debugPrint(
      '🔍 [HomePage] 初始化，导航栏回调是否存在: ${widget.onBottomNavigationBarVisibilityChanged != null}',
    );

    // 调试：检查位置服务
    _debugLocationServices();

    // 确保位置服务已启动 - 使用 WidgetsBinding 确保在widget构建后执行
    WidgetsBinding.instance.addPostFrameCallback((_) {
      debugPrint('🔍 [HomePage] PostFrameCallback 被调用，开始启动位置服务');
      _ensureLocationServiceStarted();
    });

    // 确保初始状态下导航栏是显示的
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && !_isSplitScreenMode) {
        widget.onBottomNavigationBarVisibilityChanged?.call(true);
      }
    });

    // 初始化动画控制器
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 动画初始化（保留用于未来扩展）

    _initializeData();

    // 监听地图移动，加载范围内的钓点
    mapController.mapEventStream.listen((event) {
      if (event is MapEventMoveEnd) {
        _loadSpotsInBounds();
      }
    });

    // 监听用户认证状态变化，当用户切换时重置地图数据
    Services.auth.currentUserNotifier.addListener(_onUserChanged);
  }

  @override
  void dispose() {
    // 清理定时器和监听器
    _locationUpdateTimer?.cancel();
    _locationSubscription?.cancel();
    _animationController.dispose();

    // 移除用户状态监听器
    Services.auth.currentUserNotifier.removeListener(_onUserChanged);

    super.dispose();
  }

  /// 公共方法：触发添加钓点模式（供MainScreen调用）
  void triggerAddSpotMode() {
    _toggleSplitScreenModeWithLocationName();
  }

  /// 公共方法：触发创建活动模式（供MainScreen调用）
  void triggerCreateActivityMode() {
    _toggleActivityCreateModeWithLocationName();
  }

  // 初始化数据
  Future<void> _initializeData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 静默尝试登录（不显示任何错误提示）
      try {
        if (!Services.auth.isLoggedIn) {
          await Services.auth.initialize();
        }
      } catch (e) {
        // 静默处理登录失败，不显示任何提示
        debugPrint('静默登录失败: $e');
      }

      // 初始化瓦片缓存服务
      await Services.cache.initialize();

      // 获取本地存储的位置或默认位置
      final initialLocation = Services.location.getCurrentLocation();

      setState(() {
        _userLocation = initialLocation;
        _isLoading = false;
      });

      // 移动地图到用户位置（使用最大缩放级别以便用户看到详细内容）
      mapController.move(_userLocation, 18.0);

      // 延迟加载钓点，确保地图移动完成
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _loadSpotsInBounds();
        }
      });

      // 异步获取最新位置（不阻塞UI）
      Services.location
          .requestLocationUpdate()
          .then((newLocation) {
            if (mounted) {
              setState(() {
                _userLocation = newLocation;
              });

              // 只有当位置变化较大时才移动地图（超过100米）
              final distance = Services.location.calculateDistance(
                _userLocation,
                newLocation,
              );
              if (distance > 0.1) {
                // 0.1公里 = 100米
                mapController.move(newLocation, mapController.camera.zoom);
                _loadSpotsInBounds(); // 重新加载新位置范围内的钓点
              }
            }
          })
          .catchError((e) {
            debugPrint('异步获取位置失败: $e');
          });

      // 启动定时位置更新
      _startPeriodicLocationUpdate();

      // 启动位置监听
      _startLocationListening();
    } catch (e) {
      debugPrint('初始化数据失败: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 启动定时位置更新
  void _startPeriodicLocationUpdate() {
    if (!_enablePeriodicLocationUpdate) return;

    _locationUpdateTimer = Timer.periodic(_locationUpdateInterval, (
      timer,
    ) async {
      if (!mounted) {
        timer.cancel();
        return;
      }

      try {
        final newLocation = await Services.location.requestLocationUpdate();
        if (mounted) {
          final distance = Services.location.calculateDistance(
            _userLocation,
            newLocation,
          );

          // 只有当位置变化超过50米时才更新UI
          if (distance > 0.05) {
            setState(() {
              _userLocation = newLocation;
            });

            // 如果位置变化较大（超过500米），重新加载钓点
            if (distance > 0.5) {
              _loadSpotsInBounds();
            }
          }
        }
      } catch (e) {
        debugPrint('定时位置更新失败: $e');
      }
    });
  }

  // 启动位置监听
  void _startLocationListening() {
    // 启动位置服务的实时监听
    Services.location.startLocationTracking();

    // 监听位置变化流
    _locationSubscription = Services.location.locationStream.listen(
      (LatLng newLocation) {
        if (mounted) {
          final distance = Services.location.calculateDistance(
            _userLocation,
            newLocation,
          );

          // 只有当位置变化超过20米时才更新UI
          if (distance > 0.02) {
            setState(() {
              _userLocation = newLocation;
            });

            // 如果位置变化较大（超过200米），重新加载钓点
            if (distance > 0.2) {
              _loadSpotsInBounds();
            }
          }
        }
      },
      onError: (error) {
        debugPrint('位置监听错误: $error');
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: const [
          DevMenu(), // 开发者菜单（仅在开发模式下显示）
        ],
      ),
      extendBodyBehindAppBar: true, // 让body延伸到AppBar后面
      body:
          _isSplitScreenMode ? _buildSplitScreenLayout() : _buildNormalLayout(),
      // [*参数调整*]右下角地图控制按钮（分屏模式下或搜索时隐藏）
      floatingActionButton:
          _isSplitScreenMode || _isSearching
              ? null
              : Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  SizedBox(
                    width: 50, // 缩小按钮宽度
                    height: 50, // 缩小按钮高度
                    child: FloatingActionButton(
                      onPressed: () {
                        // 切换地图类型
                        setState(() {
                          isVectorMap = !isVectorMap;
                        });
                      },
                      heroTag: 'switchMap',
                      tooltip: '切换地图类型',
                      backgroundColor: Colors.white,
                      child: FaIcon(
                        isVectorMap
                            ? FontAwesomeIcons.map
                            : FontAwesomeIcons.satellite,
                        size: 20, // 缩小图标尺寸
                      ),
                    ),
                  ),
                  const SizedBox(height: 12), // 缩小间距
                  SizedBox(
                    width: 50, // 缩小按钮宽度
                    height: 50, // 缩小按钮高度
                    child: FloatingActionButton(
                      onPressed: () {
                        // 切换注记层显示
                        setState(() {
                          showAnnotationLayer = !showAnnotationLayer;
                        });
                      },
                      heroTag: 'toggleAnnotation',
                      tooltip: '切换注记层',
                      backgroundColor: Colors.white,
                      child: FaIcon(
                        showAnnotationLayer
                            ? FontAwesomeIcons.layerGroup
                            : FontAwesomeIcons.square,
                        size: 20, // 缩小图标尺寸
                      ),
                    ),
                  ),
                  const SizedBox(height: 12), // 缩小间距
                  SizedBox(
                    width: 50, // 缩小按钮宽度
                    height: 50, // 缩小按钮高度
                    child: FloatingActionButton(
                      onPressed: () {
                        // 切换标记显示模式
                        setState(() {
                          _usePhotoMarkers = !_usePhotoMarkers;
                        });
                      },
                      heroTag: 'toggleMarkerMode',
                      tooltip: _usePhotoMarkers ? '切换到emoji标记' : '切换到照片标记',
                      backgroundColor: Colors.white,
                      child: FaIcon(
                        _usePhotoMarkers
                            ? FontAwesomeIcons.image
                            : FontAwesomeIcons.faceSmile,
                        size: 20,
                      ),
                    ),
                  ),
                  const SizedBox(height: 12), // 缩小间距
                  SizedBox(
                    width: 50, // 缩小按钮宽度
                    height: 50, // 缩小按钮高度
                    child: FloatingActionButton(
                      onPressed:
                          _isLocationResetting
                              ? null
                              : () {
                                // 重置地图位置并获取最新位置
                                _updateCurrentLocation();
                              },
                      heroTag: 'resetLocation',
                      tooltip: '重置位置',
                      backgroundColor:
                          _isLocationResetting ? Colors.grey : Colors.white,
                      child:
                          _isLocationResetting
                              ? const SizedBox(
                                width: 16, // 缩小加载指示器
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              )
                              : const FaIcon(
                                FontAwesomeIcons.locationCrosshairs,
                                size: 20,
                              ), // 缩小图标尺寸
                    ),
                  ),
                ],
              ),
    );
  }

  /// 移动地图到指定位置
  void _moveToLocation(LatLng location) {
    mapController.move(location, 15.0); // 缩放级别15
    debugPrint('地图移动到位置: ${location.latitude}, ${location.longitude}');
  }

  // 构建正常布局
  Widget _buildNormalLayout() {
    return Stack(
      children: [
        _isLoading
            ? const Center(child: CircularProgressIndicator())
            : FlutterMap(
              key: _mapKey,
              mapController: mapController,
              options: MapOptions(
                initialCenter: _userLocation,
                initialZoom: 18.0, // 设置为最大缩放级别
                minZoom: 1, // 设置最小缩放级别
                maxZoom: 18.0, // 设置最大缩放级别
                // 禁用旋转功能
                keepAlive: true,
                // 地图准备就绪时加载钓点
                onMapReady: () {
                  debugPrint('🔍 [地图] 地图准备就绪，开始加载钓点');
                  Future.delayed(const Duration(milliseconds: 100), () {
                    if (mounted) {
                      _loadSpotsInBounds();
                    }
                  });
                },
              ),
              children: [
                // 天地图瓦片层
                TileLayer(
                  urlTemplate: TianDiTuUtils.buildTileUrlTemplate(
                    isVector: isVectorMap,
                    isAnnotation: false,
                  ),
                  additionalOptions: {'k': TianDiTuUtils.key},
                  subdomains: TianDiTuUtils.subdomains,
                  // 使用带缓存的瓦片提供者提高性能
                  tileProvider: Services.cache.createCachedTileProvider(),
                ),
                // 注记层
                if (showAnnotationLayer)
                  TileLayer(
                    urlTemplate: TianDiTuUtils.buildTileUrlTemplate(
                      isVector: isVectorMap,
                      isAnnotation: true,
                    ),
                    additionalOptions: {'k': TianDiTuUtils.key},
                    subdomains: TianDiTuUtils.subdomains,
                    // 使用带缓存的瓦片提供者提高性能
                    tileProvider: Services.cache.createCachedTileProvider(),
                  ),
                // 位置标记层 - 可调整大小和对齐
                CurrentLocationLayer(
                  style: LocationMarkerStyle(
                    marker: DefaultLocationMarker(
                      child: Container(
                        width: 40,
                        height: 40,
                        alignment: Alignment.center, // 确保箭头居中
                        child: const Icon(
                          Icons.navigation,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                    markerSize: const Size(35, 35), // [*参数调整*]调整位置标记大小
                    markerDirection: MarkerDirection.heading,
                  ),
                ),
                // 添加钓点和活动标记
                MarkerLayer(
                  markers: [
                    // 钓点标记
                    for (final spot in _spots)
                      Marker(
                        point: spot.locationLatLng,
                        width: 90, // 扩大以适应新的点击区域
                        height: 90,
                        // 确保标记点始终保持竖直
                        rotate: true,
                        alignment:
                            Alignment
                                .topCenter, // [*调整参数*]地图上位置标记尖端在widget中心，使用默认居中对齐
                        child: _buildSpotMarker(spot),
                      ),
                    // 活动标记
                    for (final activity in _activities)
                      if (activity.locationLatLng != null)
                        Marker(
                          point: LatLng(
                            activity.locationLatLng!['lat']!,
                            activity.locationLatLng!['lng']!,
                          ),
                          width: 90,
                          height: 90,
                          rotate: true,
                          alignment: Alignment.topCenter,
                          child: _buildActivityMarker(activity),
                        ),
                  ],
                ),
              ],
            ),

        // 优化后的搜索栏
        OptimizedSearchBar(
          currentLocation: _userLocation,
          onLocationSelected: _moveToLocation,
          hintText: '搜索钓点、地址',
          onSearchStarted: () {
            setState(() {
              _isSearching = true;
            });
            // 搜索时隐藏底部导航栏
            widget.onBottomNavigationBarVisibilityChanged?.call(false);
          },
          onSearchEnded: () {
            setState(() {
              _isSearching = false;
            });
            // 搜索结束时显示底部导航栏
            widget.onBottomNavigationBarVisibilityChanged?.call(true);
          },
        ),
      ],
    );
  }

  // 构建分屏布局
  Widget _buildSplitScreenLayout() {
    return Stack(
      children: [
        // 全屏地图作为背景
        FlutterMap(
          key: _mapKey,
          mapController: mapController,
          options: MapOptions(
            initialCenter: _userLocation,
            initialZoom: 18.0, // 设置为最大缩放级别
            minZoom: 1,
            maxZoom: 18.0,
            keepAlive: true,
            // 地图准备就绪时加载钓点
            onMapReady: () {
              debugPrint('🔍 [地图] 地图准备就绪，开始加载钓点');
              Future.delayed(const Duration(milliseconds: 100), () {
                if (mounted) {
                  _loadSpotsInBounds();
                }
              });
            },
            // 地图移动时更新中心标记位置 - 基于屏幕25%高度处
            onPositionChanged: (position, hasGesture) {
              if (hasGesture) {
                setState(() {
                  // 计算屏幕25%高度处对应的地图坐标
                  _centerMarkerLocation = _calculateLocationAt25PercentHeight();
                });
              }
            },
          ),
          children: [
            // 底图层
            TileLayer(
              urlTemplate: TianDiTuUtils.buildTileUrlTemplate(
                isVector: isVectorMap,
                isAnnotation: false,
              ),
              additionalOptions: {'k': TianDiTuUtils.key},
              subdomains: TianDiTuUtils.subdomains,
              userAgentPackageName: 'com.example.fishing_app',
            ),

            // 注记层
            if (showAnnotationLayer)
              TileLayer(
                urlTemplate: TianDiTuUtils.buildTileUrlTemplate(
                  isVector: isVectorMap,
                  isAnnotation: true,
                ),
                additionalOptions: {'k': TianDiTuUtils.key},
                subdomains: TianDiTuUtils.subdomains,
                userAgentPackageName: 'com.example.fishing_app',
              ),

            // 添加钓点标记
            // 位置标记层 - 可调整大小和对齐
            CurrentLocationLayer(
              style: LocationMarkerStyle(
                marker: DefaultLocationMarker(
                  child: Container(
                    width: 40,
                    height: 40,
                    alignment: Alignment.center, // 确保箭头居中
                    child: const Icon(
                      Icons.navigation,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
                markerSize: const Size(35, 35), // [*参数调整*]调整位置标记大小
                markerDirection: MarkerDirection.heading,
              ),
            ),
            MarkerLayer(
              markers: [
                // 钓点标记
                for (final spot in _spots)
                  Marker(
                    point: spot.locationLatLng,
                    width: 90, // 扩大以适应新的点击区域
                    height: 90,
                    rotate: true,
                    alignment: Alignment.center, // 尖端在widget中心，使用默认居中对齐
                    child: _buildSpotMarker(spot),
                  ),
                // 活动标记
                for (final activity in _activities)
                  if (activity.locationLatLng != null)
                    Marker(
                      point: LatLng(
                        activity.locationLatLng!['lat']!,
                        activity.locationLatLng!['lng']!,
                      ),
                      width: 90,
                      height: 90,
                      rotate: true,
                      alignment: Alignment.center,
                      child: _buildActivityMarker(activity),
                    ),
              ],
            ),
          ],
        ),

        // 中心标记覆盖层 - 位于屏幕顶部往下25%处
        Positioned(
          left: 0,
          right: 0,
          top: MarkerAlignmentUtils.calculatePinOffset(
            screenHeight: MediaQuery.of(context).size.height,
            targetHeightPercent: 0.25,
            pinSize: 40.0,
          ), // 精确计算图钉偏移，使针尖指向坐标点
          child: const Text(
            '📍',
            style: TextStyle(fontSize: 40),
            textAlign: TextAlign.center,
          ),
        ),

        // 底部表单（根据模式显示不同组件）
        if (_isActivityCreateMode)
          SplitScreenActivityCreate(
            location: _centerMarkerLocation,
            suggestedTitle: _suggestedActivityTitle,
            onLocationChanged: (newLocation) {
              setState(() {
                _centerMarkerLocation = newLocation;
              });
            },
            onClose: () {
              _toggleSplitScreenMode();
            },
            onActivityCreated: (activity) {
              setState(() {
                _activities.add(activity);
              });

              // 设置新活动位置作为目标位置，这样退出分屏模式时会移动到这里
              _originalMapCenter = activity.locationAsLatLng;

              // 关闭分屏模式（会自动移动到_originalMapCenter位置）
              _toggleSplitScreenMode();
            },
          )
        else
          SplitScreenAddSpot(
            location: _centerMarkerLocation,
            suggestedName: _suggestedSpotName,
            onLocationChanged: (newLocation) {
              setState(() {
                _centerMarkerLocation = newLocation;
              });
            },
            onClose: () {
              _toggleSplitScreenMode();
            },
            onSpotAdded: (spot) {
              setState(() {
                _spots.add(spot);
              });

              // 设置新钓点位置作为目标位置，这样退出分屏模式时会移动到这里
              final spotLocation = spot.locationLatLng;
              _originalMapCenter = spotLocation;

              // 关闭分屏模式（会自动移动到_originalMapCenter位置）
              _toggleSplitScreenMode();
            },
          ),
      ],
    );
  }

  // 切换分屏模式
  void _toggleSplitScreenMode() {
    final wasInSplitScreenMode = _isSplitScreenMode;

    setState(() {
      _isSplitScreenMode = !_isSplitScreenMode;
      if (_isSplitScreenMode) {
        // 进入分屏模式时，设置中心标记位置为屏幕25%高度处
        _centerMarkerLocation = _calculateLocationAt25PercentHeight();
      } else {
        // 退出分屏模式时，恢复原来的地图中心位置
        if (_originalMapCenter != null) {
          mapController.move(_originalMapCenter!, mapController.camera.zoom);
          _originalMapCenter = null; // 清除保存的位置
        }
        // 清除建议的名称
        _suggestedSpotName = null;
        _suggestedActivityTitle = null;
        _isActivityCreateMode = false; // 重置活动创建模式
      }
    });

    // 使用postFrameCallback确保在正确的时机调用回调
    if (_isSplitScreenMode && !wasInSplitScreenMode) {
      // 刚进入分屏模式，隐藏底部导航栏
      debugPrint('🔍 [HomePage] 进入添加钓点模式，准备隐藏底部导航栏');
      debugPrint(
        '🔍 [HomePage] 回调函数是否存在: ${widget.onBottomNavigationBarVisibilityChanged != null}',
      );
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          widget.onBottomNavigationBarVisibilityChanged?.call(false);
          debugPrint('🔍 [HomePage] 已调用隐藏导航栏回调');
        }
      });
    } else if (!_isSplitScreenMode && wasInSplitScreenMode) {
      // 刚退出分屏模式，显示底部导航栏
      debugPrint('🔍 [HomePage] 退出添加钓点模式，准备显示底部导航栏');
      debugPrint(
        '🔍 [HomePage] 回调函数是否存在: ${widget.onBottomNavigationBarVisibilityChanged != null}',
      );
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          widget.onBottomNavigationBarVisibilityChanged?.call(true);
          debugPrint('🔍 [HomePage] 已调用显示导航栏回调');
        }
      });
    }
  }

  // 切换创建活动模式并获取位置名称
  Future<void> _toggleActivityCreateModeWithLocationName() async {
    if (_isSplitScreenMode) {
      // 如果已经在分屏模式，直接退出
      _toggleSplitScreenMode();
      return;
    }

    // 检查用户是否已登录
    if (!Services.auth.isLoggedIn) {
      // 显示登录提示对话框
      _showLoginRequiredDialog();
      return;
    }

    // 1. 读取屏幕中心点的经纬度坐标
    final screenCenterCoordinate = mapController.camera.center;
    debugPrint(
      '🔍 [地图移动] 屏幕中心坐标: ${screenCenterCoordinate.latitude}, ${screenCenterCoordinate.longitude}',
    );

    // 保存当前地图中心位置，用于退出时恢复
    _originalMapCenter = screenCenterCoordinate;

    // 2. 将该坐标移动到屏幕25%高度、50%宽度处
    _moveCoordinateToScreenPosition(
      screenCenterCoordinate,
      screenHeightPercent: 0.25,
      screenWidthPercent: 0.50,
    );

    // 3. 设置分屏模式状态并打开创建活动页面
    setState(() {
      _isSplitScreenMode = true;
      _isActivityCreateMode = true; // 标记为创建活动模式
      _centerMarkerLocation = screenCenterCoordinate;
    });

    // 隐藏底部导航栏
    debugPrint('🔍 [HomePage] 进入创建活动模式，准备隐藏底部导航栏');
    widget.onBottomNavigationBarVisibilityChanged?.call(false);

    // 异步获取位置名称作为活动标题建议
    try {
      final result = await TianDiTuUtils.reverseGeocode(
        screenCenterCoordinate.longitude,
        screenCenterCoordinate.latitude,
      );

      if (result != null && mounted) {
        final locationName = result['formatted_address'] ?? result['address'];
        if (locationName != null) {
          setState(() {
            _suggestedActivityTitle = '$locationName 钓鱼活动';
          });
        }
      }
    } catch (e) {
      debugPrint('❌ [活动创建] 获取位置名称失败: $e');
    }
  }

  // 切换分屏模式并获取位置名称
  Future<void> _toggleSplitScreenModeWithLocationName() async {
    if (_isSplitScreenMode) {
      // 如果已经在分屏模式，直接退出
      _toggleSplitScreenMode();
      return;
    }

    // 检查用户是否已登录
    if (!Services.auth.isLoggedIn) {
      // 显示登录提示对话框
      _showLoginRequiredDialog();
      return;
    }

    // 1. 读取屏幕中心点的经纬度坐标
    final screenCenterCoordinate = mapController.camera.center;
    debugPrint(
      '🔍 [地图移动] 屏幕中心坐标: ${screenCenterCoordinate.latitude}, ${screenCenterCoordinate.longitude}',
    );

    // 保存当前地图中心位置，用于退出时恢复
    _originalMapCenter = screenCenterCoordinate;

    // 2. 将该坐标移动到屏幕25%高度、50%宽度处
    _moveCoordinateToScreenPosition(
      screenCenterCoordinate,
      screenHeightPercent: 0.25,
      screenWidthPercent: 0.50,
    );

    // 3. 验证坐标转换准确性（调试用）
    final screenSize = MediaQuery.of(context).size;
    MapCoordinateUtils.validateCoordinateConversion(
      mapController.camera,
      screenCenterCoordinate,
      screenSize,
    );

    // 4. 设置分屏模式状态并打开添加钓点页面
    setState(() {
      _isSplitScreenMode = true;
      _centerMarkerLocation = screenCenterCoordinate; // 标记位置就是原来的屏幕中心
    });

    // 隐藏底部导航栏
    debugPrint('🔍 [HomePage] 进入添加钓点模式，准备隐藏底部导航栏');
    debugPrint(
      '🔍 [HomePage] 回调函数是否存在: ${widget.onBottomNavigationBarVisibilityChanged != null}',
    );
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        widget.onBottomNavigationBarVisibilityChanged?.call(false);
        debugPrint('🔍 [HomePage] 已调用隐藏导航栏回调');
      }
    });

    // 异步获取位置名称
    try {
      debugPrint(
        '开始获取位置名称: ${screenCenterCoordinate.latitude}, ${screenCenterCoordinate.longitude}',
      );

      final locationName = await _getTianDiTuLocationName(
        screenCenterCoordinate.longitude,
        screenCenterCoordinate.latitude,
      );

      if (locationName != null && locationName.trim().isNotEmpty) {
        setState(() {
          _suggestedSpotName = '${locationName.trim()}钓点';
        });
        debugPrint('获取到位置名称: $_suggestedSpotName');
      } else {
        debugPrint('未获取到有效的位置名称');
        setState(() {
          _suggestedSpotName = null;
        });
      }
    } catch (e) {
      debugPrint('获取位置名称失败: $e');
      setState(() {
        _suggestedSpotName = null;
      });
    }
  }

  // 直接调用天地图逆地理编码API
  Future<String?> _getTianDiTuLocationName(
    double longitude,
    double latitude,
  ) async {
    try {
      // 使用真实的天地图API调用
      debugPrint('开始调用天地图API获取位置名称: 经度=$longitude, 纬度=$latitude');

      final locationName = await TianDiTuUtils.getBestLocationName(
        longitude,
        latitude,
      );

      if (locationName != null && locationName.trim().isNotEmpty) {
        debugPrint('天地图API返回位置名称: $locationName');
        return locationName;
      } else {
        debugPrint('天地图API未返回有效位置名称，使用模拟数据');

        // 如果API调用失败，使用模拟数据作为备选
        if (latitude > 39.9 &&
            latitude < 40.0 &&
            longitude > 116.3 &&
            longitude < 116.5) {
          return '天安门广场';
        } else if (latitude > 31.2 &&
            latitude < 31.3 &&
            longitude > 121.4 &&
            longitude < 121.5) {
          return '外滩';
        } else if (latitude > 22.5 &&
            latitude < 22.6 &&
            longitude > 114.0 &&
            longitude < 114.2) {
          return '维多利亚港';
        } else {
          return '未知地点';
        }
      }
    } catch (e) {
      debugPrint('获取位置名称失败: $e，使用模拟数据');

      // 异常时使用模拟数据作为备选
      if (latitude > 39.9 &&
          latitude < 40.0 &&
          longitude > 116.3 &&
          longitude < 116.5) {
        return '天安门广场';
      } else {
        return '未知地点';
      }
    }
  }

  // 用于防止频繁加载的变量
  bool _isLoadingSpots = false;
  DateTime _lastLoadTime = DateTime.now();

  // 已加载的区域范围（用于增量加载）
  double? _loadedMinLat;
  double? _loadedMaxLat;
  double? _loadedMinLng;
  double? _loadedMaxLng;

  // 已加载的钓点和活动集合（避免重复）
  final Set<String> _loadedSpotIds = {};
  final Set<String> _loadedActivityIds = {};

  // 根据地图范围加载钓点和活动（增量加载）
  Future<void> _loadSpotsInBounds() async {
    // 如果页面正在加载或者已经在加载钓点，则跳过
    if (_isLoading || _isLoadingSpots) return;

    // 防止频繁加载：如果距离上次加载不足500毫秒，则跳过
    final now = DateTime.now();
    if (now.difference(_lastLoadTime).inMilliseconds < 500) return;

    _isLoadingSpots = true;
    _lastLoadTime = now;

    final bounds = mapController.camera.visibleBounds;

    // 计算2倍范围的边界
    final latRange = bounds.north - bounds.south;
    final lngRange = bounds.east - bounds.west;

    final expandedMinLat = bounds.south - latRange;
    final expandedMaxLat = bounds.north + latRange;
    final expandedMinLng = bounds.west - lngRange;
    final expandedMaxLng = bounds.east + lngRange;

    try {
      List<List<double>> newRegions = [];

      if (_loadedMinLat == null) {
        // 首次加载：加载2倍范围的所有钓点
        debugPrint(
          '🔍 [钓点加载] 首次加载，范围: ${expandedMinLat.toStringAsFixed(4)} - ${expandedMaxLat.toStringAsFixed(4)}, ${expandedMinLng.toStringAsFixed(4)} - ${expandedMaxLng.toStringAsFixed(4)}',
        );
        newRegions.add([
          expandedMinLat,
          expandedMaxLat,
          expandedMinLng,
          expandedMaxLng,
        ]);

        // 更新已加载范围
        _loadedMinLat = expandedMinLat;
        _loadedMaxLat = expandedMaxLat;
        _loadedMinLng = expandedMinLng;
        _loadedMaxLng = expandedMaxLng;
      } else {
        // 增量加载：只加载新增区域
        newRegions = _calculateNewRegions(
          expandedMinLat,
          expandedMaxLat,
          expandedMinLng,
          expandedMaxLng,
          _loadedMinLat!,
          _loadedMaxLat!,
          _loadedMinLng!,
          _loadedMaxLng!,
        );

        if (newRegions.isNotEmpty) {
          debugPrint('🔍 [钓点加载] 增量加载，发现 ${newRegions.length} 个新区域');

          // 更新已加载范围为并集
          _loadedMinLat = math.min(_loadedMinLat!, expandedMinLat);
          _loadedMaxLat = math.max(_loadedMaxLat!, expandedMaxLat);
          _loadedMinLng = math.min(_loadedMinLng!, expandedMinLng);
          _loadedMaxLng = math.max(_loadedMaxLng!, expandedMaxLng);
        } else {
          debugPrint('🔍 [钓点加载] 无新区域需要加载');
          _isLoadingSpots = false;
          return;
        }
      }

      // 加载新区域的钓点和活动
      final List<FishingSpot> newSpots = [];
      final List<FishingActivity> newActivities = [];

      for (final region in newRegions) {
        // 计算区域中心点和半径
        final centerLat = (region[0] + region[1]) / 2;
        final centerLng = (region[2] + region[3]) / 2;
        final center = LatLng(centerLat, centerLng);

        // 计算半径（取较大的维度）
        const double kmPerDegree = 111.0;
        final latRange = (region[1] - region[0]) * kmPerDegree;
        final lngRange =
            (region[3] - region[2]) *
            kmPerDegree *
            math.cos(centerLat * math.pi / 180);
        final radiusKm = math.max(latRange, lngRange) / 2;

        // 并行加载钓点和活动
        final futures = await Future.wait([
          Services.fishingSpot.getVisibleSpots(
            center: center,
            radiusKm: radiusKm,
            perPage: 100,
          ),
          Services.activity.getNearbyActivities(
            center: center,
            radiusKm: radiusKm,
            perPage: 50,
          ),
        ]);

        final regionSpots = futures[0] as List<FishingSpot>;
        final regionActivities = futures[1] as List<FishingActivity>;

        // 过滤掉已加载的钓点
        for (final spot in regionSpots) {
          if (!_loadedSpotIds.contains(spot.id)) {
            newSpots.add(spot);
            _loadedSpotIds.add(spot.id);
          }
        }

        // 过滤掉已加载的活动
        for (final activity in regionActivities) {
          if (!_loadedActivityIds.contains(activity.id)) {
            newActivities.add(activity);
            _loadedActivityIds.add(activity.id);
          }
        }
      }

      if (mounted && (newSpots.isNotEmpty || newActivities.isNotEmpty)) {
        setState(() {
          _spots.addAll(newSpots);
          _activities.addAll(newActivities);
          debugPrint(
            '🔍 [数据加载] 新增 ${newSpots.length} 个钓点，${newActivities.length} 个活动，总计 ${_spots.length} 个钓点，${_activities.length} 个活动',
          );
        });
      }
    } catch (e) {
      debugPrint('加载范围内钓点失败: $e');
    } finally {
      _isLoadingSpots = false;
    }
  }

  // 计算需要加载的新区域
  List<List<double>> _calculateNewRegions(
    double newMinLat,
    double newMaxLat,
    double newMinLng,
    double newMaxLng,
    double oldMinLat,
    double oldMaxLat,
    double oldMinLng,
    double oldMaxLng,
  ) {
    List<List<double>> regions = [];

    // 检查是否有重叠
    if (newMaxLat < oldMinLat ||
        newMinLat > oldMaxLat ||
        newMaxLng < oldMinLng ||
        newMinLng > oldMaxLng) {
      // 完全不重叠，加载整个新区域
      regions.add([newMinLat, newMaxLat, newMinLng, newMaxLng]);
      return regions;
    }

    // 计算新增的区域（矩形分割）

    // 上方新区域
    if (newMaxLat > oldMaxLat) {
      regions.add([oldMaxLat, newMaxLat, newMinLng, newMaxLng]);
    }

    // 下方新区域
    if (newMinLat < oldMinLat) {
      regions.add([newMinLat, oldMinLat, newMinLng, newMaxLng]);
    }

    // 左侧新区域（排除已包含在上下区域的部分）
    if (newMinLng < oldMinLng) {
      final topLat = math.min(newMaxLat, oldMaxLat);
      final bottomLat = math.max(newMinLat, oldMinLat);
      if (topLat > bottomLat) {
        regions.add([bottomLat, topLat, newMinLng, oldMinLng]);
      }
    }

    // 右侧新区域（排除已包含在上下区域的部分）
    if (newMaxLng > oldMaxLng) {
      final topLat = math.min(newMaxLat, oldMaxLat);
      final bottomLat = math.max(newMinLat, oldMinLat);
      if (topLat > bottomLat) {
        regions.add([bottomLat, topLat, oldMaxLng, newMaxLng]);
      }
    }

    return regions;
  }

  // 重置已加载的区域状态
  void _resetLoadedRegions() {
    _loadedMinLat = null;
    _loadedMaxLat = null;
    _loadedMinLng = null;
    _loadedMaxLng = null;
    _loadedSpotIds.clear();
    _loadedActivityIds.clear();
    _spots.clear();
    _activities.clear();
    debugPrint('🔍 [数据加载] 重置加载状态');
  }

  // 用户状态变化回调
  void _onUserChanged() {
    if (!mounted) return;

    debugPrint('🔄 [地图页面] 用户状态发生变化，重置地图数据');

    // 重置已加载的钓点数据
    _resetLoadedRegions();

    // 重新加载当前视图范围内的钓点
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        _loadSpotsInBounds();
      }
    });

    // 更新UI
    if (mounted) {
      setState(() {
        // 触发UI重建，显示新用户的钓点数据
      });
    }
  }

  // 更新当前位置并移动地图
  Future<void> _updateCurrentLocation() async {
    if (_isLocationResetting) return;

    setState(() {
      _isLocationResetting = true;
    });

    try {
      // 异步获取最新位置
      final newLocation = await Services.location.requestLocationUpdate();

      if (mounted) {
        setState(() {
          _userLocation = newLocation;
          _isLocationResetting = false;
        });

        // 保持当前缩放级别，移动地图到新位置
        final currentZoom = mapController.camera.zoom;
        mapController.move(_userLocation, currentZoom);

        // 显示成功提示
        SnackBarService.showSuccess(context, '位置已更新');

        // 重置加载状态并重新加载钓点
        _resetLoadedRegions();
        _loadSpotsInBounds();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLocationResetting = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('获取位置失败: $e')),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// 确保位置服务已启动
  Future<void> _ensureLocationServiceStarted() async {
    try {
      debugPrint('🔍 [LocationService] 确保位置服务已启动...');

      // 启动位置监听
      await Services.location.startLocationTracking();

      // 请求一次位置更新以确保有初始位置
      await Services.location.requestLocationUpdate();

      debugPrint('🔍 [LocationService] 位置服务启动完成');
    } catch (e) {
      debugPrint('🔍 [LocationService] 启动失败: $e');
    }
  }

  /// 调试位置服务
  Future<void> _debugLocationServices() async {
    try {
      debugPrint('🔍 [LocationDebug] 开始检查位置服务...');

      // 检查位置服务是否启用
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      debugPrint('🔍 [LocationDebug] 位置服务启用: $serviceEnabled');

      // 检查权限
      LocationPermission permission = await Geolocator.checkPermission();
      debugPrint('🔍 [LocationDebug] 当前权限: $permission');

      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        debugPrint('🔍 [LocationDebug] 请求权限后: $permission');
      }

      // 尝试获取位置
      if (serviceEnabled &&
          permission != LocationPermission.denied &&
          permission != LocationPermission.deniedForever) {
        try {
          Position position = await Geolocator.getCurrentPosition(
            locationSettings: const LocationSettings(
              accuracy: LocationAccuracy.medium,
              timeLimit: Duration(seconds: 10),
            ),
          );
          debugPrint(
            '🔍 [LocationDebug] 当前位置: ${position.latitude}, ${position.longitude}',
          );
          debugPrint('🔍 [LocationDebug] 精度: ${position.accuracy}米');
        } catch (e) {
          debugPrint('🔍 [LocationDebug] 获取位置失败: $e');
        }
      } else {
        debugPrint('🔍 [LocationDebug] 位置服务不可用或权限不足');
      }
    } catch (e) {
      debugPrint('🔍 [LocationDebug] 检查失败: $e');
    }
  }

  // 显示钓点详情
  void _showSpotDetails(FishingSpot spot) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => SpotDetailPage(spot: spot)),
    );
  }

  // 将指定坐标移动到屏幕的指定位置（使用工具类）
  void _moveCoordinateToScreenPosition(
    LatLng targetCoordinate, {
    required double screenHeightPercent,
    required double screenWidthPercent,
  }) {
    final screenSize = MediaQuery.of(context).size;

    MapCoordinateUtils.moveCoordinateToScreenPosition(
      mapController,
      targetCoordinate,
      screenHeightPercent: screenHeightPercent,
      screenWidthPercent: screenWidthPercent,
      screenSize: screenSize,
    );
  }

  // 计算屏幕25%高度处对应的地图坐标（使用工具类）
  LatLng _calculateLocationAt25PercentHeight() {
    final screenSize = MediaQuery.of(context).size;
    final camera = mapController.camera;

    return MapCoordinateUtils.calculateLocationAt25PercentHeight(
      camera,
      screenSize,
    );
  }

  /// 构建钓点标记（根据模式选择照片或emoji）
  Widget _buildSpotMarker(FishingSpot spot) {
    if (_usePhotoMarkers) {
      // 使用照片标记
      return PhotoFishingSpotMarkerBuilder.buildMarker(
        spot: spot,
        onTap: () => _showSpotDetails(spot),
        size: 60.0,
        getPhotos: (spotId) => Services.fishingSpot.getSpotPhotos(spotId),
        getLikesCount: (spotId) => Services.social.getSpotLikesCount(spotId),
      );
    } else {
      // 使用emoji标记
      return FishingSpotMarkerBuilder.buildMarker(
        spot: spot,
        onTap: () => _showSpotDetails(spot),
        size: 60.0,
        getLikesCount: (spotId) => Services.social.getSpotLikesCount(spotId),
      );
    }
  }

  /// 构建活动标记
  Widget _buildActivityMarker(FishingActivity activity) {
    return GestureDetector(
      onTap: () => _showActivityDetails(activity),
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(color: Colors.green, width: 3),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipOval(
          child:
              activity.primaryImage != null
                  ? Image.network(
                    activity.primaryImage!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return _buildActivityFallbackIcon(activity);
                    },
                  )
                  : _buildActivityFallbackIcon(activity),
        ),
      ),
    );
  }

  /// 构建活动标记的后备图标
  Widget _buildActivityFallbackIcon(FishingActivity activity) {
    return Container(
      color: Colors.green.shade100,
      child: const Icon(Icons.group, color: Colors.green, size: 30),
    );
  }

  /// 显示活动详情
  void _showActivityDetails(FishingActivity activity) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ActivityDetailPage(activity: activity),
      ),
    );
  }

  /// 显示登录提示对话框
  void _showLoginRequiredDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('需要登录'),
          content: const Text('添加钓点需要先登录账户。是否前往登录页面？'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // 导航到登录页面
                Navigator.of(context).pushNamed('/login');
              },
              child: const Text('去登录'),
            ),
          ],
        );
      },
    );
  }
}
