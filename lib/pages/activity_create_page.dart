import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';

import '../services/service_locator.dart';
import '../theme/unified_theme.dart';
import '../widgets/add_spot_form/image_upload_widget.dart';
import '../widgets/add_spot_form/image_upload_manager.dart';
import '../utils/tianditu_utils.dart';

/// 创建活动页面
class ActivityCreatePage extends StatefulWidget {
  const ActivityCreatePage({super.key});

  @override
  State<ActivityCreatePage> createState() => _ActivityCreatePageState();
}

class _ActivityCreatePageState extends State<ActivityCreatePage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationNameController = TextEditingController();

  // 图片上传相关
  final List<ImageUploadItem> _selectedImages = [];
  final ImageUploadManager _imageUploadManager = ImageUploadManager();

  // 位置和时间相关
  late LatLng _selectedLocation;
  DateTime? _startTime;
  DateTime? _endTime;
  bool _isSubmitting = false;
  bool _isRefreshingLocationName = false;

  @override
  void initState() {
    super.initState();
    // 获取用户当前位置作为默认位置
    _selectedLocation = Services.location.getCurrentLocation();

    // 自动获取地名
    _refreshLocationName();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _locationNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('发起一起钓鱼'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: _canSubmit() ? _submitForm : null,
            child: Text(
              _imageUploadManager.hasUploadingImages(_selectedImages)
                  ? _imageUploadManager.getUploadStatusText(_selectedImages)
                  : '发布',
              style: AppTheme.labelLarge.copyWith(
                color:
                    _canSubmit()
                        ? AppTheme.designPrimaryColor
                        : AppTheme.textHintColor,
              ),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppTheme.spacingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 活动标题（带自动填充功能）
              _buildFormCard(child: _buildTitleInput()),

              const SizedBox(height: AppTheme.spacingM),

              // 图片上传
              _buildFormCard(
                child: ImageUploadWidget(
                  selectedImages: _selectedImages,
                  onImagesAdded: _handleImagesAdded,
                  onImageRemoved: _handleImageRemoved,
                ),
              ),

              const SizedBox(height: AppTheme.spacingM),

              // 活动描述
              _buildFormCard(
                child: TextFormField(
                  controller: _descriptionController,
                  maxLines: 4,
                  decoration: const InputDecoration(
                    labelText: '活动描述',
                    hintText: '描述一下这次钓鱼活动的详情...',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '请输入活动描述';
                    }
                    return null;
                  },
                ),
              ),

              const SizedBox(height: AppTheme.spacingM),

              // 活动地点
              _buildFormCard(child: _buildLocationInput()),

              const SizedBox(height: AppTheme.spacingM),

              // 活动时间
              _buildFormCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 开始时间
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      leading: const Icon(Icons.access_time),
                      title: const Text('开始时间'),
                      subtitle: Text(
                        _startTime != null
                            ? '${_startTime!.year}-${_startTime!.month.toString().padLeft(2, '0')}-${_startTime!.day.toString().padLeft(2, '0')} ${_startTime!.hour.toString().padLeft(2, '0')}:${_startTime!.minute.toString().padLeft(2, '0')}'
                            : '请选择开始时间',
                      ),
                      onTap: () => _selectDateTime(true),
                    ),
                    const Divider(),
                    // 结束时间
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      leading: const Icon(Icons.access_time_filled),
                      title: const Text('结束时间'),
                      subtitle: Text(
                        _endTime != null
                            ? '${_endTime!.year}-${_endTime!.month.toString().padLeft(2, '0')}-${_endTime!.day.toString().padLeft(2, '0')} ${_endTime!.hour.toString().padLeft(2, '0')}:${_endTime!.minute.toString().padLeft(2, '0')}'
                            : '请选择结束时间',
                      ),
                      onTap: () => _selectDateTime(false),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 刷新位置名称
  Future<void> _refreshLocationName() async {
    if (_isRefreshingLocationName) return;

    setState(() {
      _isRefreshingLocationName = true;
    });

    try {
      final result = await TianDiTuUtils.reverseGeocode(
        _selectedLocation.longitude,
        _selectedLocation.latitude,
      );

      if (result != null && mounted) {
        final locationName = result['formatted_address'] ?? result['address'];
        if (locationName != null) {
          _locationNameController.text = locationName;

          // 如果标题为空，自动填充标题
          if (_titleController.text.isEmpty) {
            _titleController.text = '$locationName 钓鱼活动';
          }
        }
      }
    } catch (e) {
      debugPrint('❌ [活动创建] 获取位置名称失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshingLocationName = false;
        });
      }
    }
  }

  /// 处理图片添加
  void _handleImagesAdded(List<ImageUploadItem> newImages) {
    setState(() {
      _selectedImages.addAll(newImages);
    });

    // 立即开始上传图片
    _imageUploadManager.uploadSelectedImages(newImages, (updatedItem) {
      setState(() {
        // 更新图片状态
      });
    });
  }

  /// 处理图片移除
  void _handleImageRemoved(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  /// 构建标题输入组件
  Widget _buildTitleInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('活动标题', style: AppTheme.headlineSmall),
        const SizedBox(height: AppTheme.spacingS),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: '活动标题 *',
                  hintText: '给你的钓鱼活动起个名字',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入活动标题';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: AppTheme.spacingS),
            IconButton(
              onPressed:
                  _isRefreshingLocationName ? null : _refreshLocationName,
              icon:
                  _isRefreshingLocationName
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Icon(Icons.refresh),
              tooltip: '根据位置自动填充标题',
            ),
          ],
        ),
      ],
    );
  }

  /// 构建位置输入组件
  Widget _buildLocationInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('活动地点', style: AppTheme.headlineSmall),
        const SizedBox(height: AppTheme.spacingS),
        TextFormField(
          controller: _locationNameController,
          decoration: const InputDecoration(
            labelText: '活动地点 *',
            hintText: '输入具体地点名称',
            border: OutlineInputBorder(),
            suffixIcon: Icon(Icons.location_on),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入活动地点';
            }
            return null;
          },
        ),
        const SizedBox(height: AppTheme.spacingS),
        Text(
          '坐标: ${_selectedLocation.latitude.toStringAsFixed(6)}, ${_selectedLocation.longitude.toStringAsFixed(6)}',
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
      ],
    );
  }

  /// 检查是否可以提交
  bool _canSubmit() {
    return !_isSubmitting &&
        !_imageUploadManager.hasUploadingImages(_selectedImages);
  }

  /// 构建表单卡片
  Widget _buildFormCard({required Widget child}) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: child,
    );
  }

  /// 选择日期时间
  Future<void> _selectDateTime(bool isStartTime) async {
    final now = DateTime.now();
    final initialDate =
        isStartTime
            ? (_startTime ?? now.add(const Duration(hours: 1)))
            : (_endTime ??
                _startTime?.add(const Duration(hours: 2)) ??
                now.add(const Duration(hours: 3)));

    final date = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: now,
      lastDate: now.add(const Duration(days: 365)),
    );

    if (date != null && mounted) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(initialDate),
      );

      if (time != null && mounted) {
        final selectedDateTime = DateTime(
          date.year,
          date.month,
          date.day,
          time.hour,
          time.minute,
        );

        setState(() {
          if (isStartTime) {
            _startTime = selectedDateTime;
            // 如果结束时间早于开始时间，自动调整结束时间
            if (_endTime != null && _endTime!.isBefore(selectedDateTime)) {
              _endTime = selectedDateTime.add(const Duration(hours: 2));
            }
          } else {
            _endTime = selectedDateTime;
          }
        });
      }
    }
  }

  /// 提交表单
  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    if (_startTime == null || _endTime == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请选择活动时间')));
      return;
    }

    if (_endTime!.isBefore(_startTime!)) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('结束时间不能早于开始时间')));
      return;
    }

    // 位置已在初始化时设置，无需检查

    setState(() => _isSubmitting = true);

    try {
      final activity = await Services.activity.createActivity(
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        location: _selectedLocation,
        locationName: _locationNameController.text.trim(),
        startTime: _startTime!,
        endTime: _endTime!,
        // 移除最大参与人数限制
      );

      if (activity != null && mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('活动创建成功！')));
        Navigator.of(context).pop(activity);
      } else if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('创建活动失败，请重试')));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('创建活动失败: $e')));
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }
}
