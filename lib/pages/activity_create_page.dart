import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';

import '../services/service_locator.dart';

import '../widgets/add_activity_form/add_activity_form.dart';

/// 创建活动页面
class ActivityCreatePage extends StatefulWidget {
  const ActivityCreatePage({super.key});

  @override
  State<ActivityCreatePage> createState() => _ActivityCreatePageState();
}

class _ActivityCreatePageState extends State<ActivityCreatePage> {
  late LatLng _selectedLocation;

  @override
  void initState() {
    super.initState();
    // 获取用户当前位置作为默认位置
    _selectedLocation = Services.location.getCurrentLocation();
  }

  @override
  Widget build(BuildContext context) {
    return AddActivityForm(
      location: _selectedLocation,
      onSubmitSuccess: (activity) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('活动创建成功！')));
        Navigator.of(context).pop(activity);
      },
      onCancel: () {
        Navigator.of(context).pop();
      },
    );
  }
}
