import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../models/fishing_activity.dart';
import '../models/activity_participant.dart';
import '../models/group_chat.dart';
import '../services/service_locator.dart';
import '../theme/unified_theme.dart';
import 'group_chat_page.dart';

/// 活动详情页面
class ActivityDetailPage extends StatefulWidget {
  /// 活动数据
  final FishingActivity activity;

  const ActivityDetailPage({super.key, required this.activity});

  @override
  State<ActivityDetailPage> createState() => _ActivityDetailPageState();
}

class _ActivityDetailPageState extends State<ActivityDetailPage> {
  late FishingActivity _activity;
  List<ActivityParticipant> _participants = [];
  GroupChat? _groupChat;
  bool _isLoading = true;
  bool _isJoining = false;
  bool _isUserParticipant = false;

  @override
  void initState() {
    super.initState();
    _activity = widget.activity;
    _loadActivityDetails();
  }

  /// 加载活动详情
  Future<void> _loadActivityDetails() async {
    setState(() => _isLoading = true);

    try {
      // 并行加载参与者列表、群聊信息和用户参与状态
      final futures = await Future.wait([
        Services.activity.getActivityParticipants(_activity.id),
        Services.groupChat.getGroupChatByActivityId(_activity.id),
        _checkUserParticipation(),
      ]);

      final participants = futures[0] as List<ActivityParticipant>;
      final groupChat = futures[1] as GroupChat?;
      final isParticipant = futures[2] as bool;

      if (mounted) {
        setState(() {
          _participants = participants;
          _groupChat = groupChat;
          _isUserParticipant = isParticipant;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('❌ [活动详情] 加载详情失败: $e');
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// 检查用户是否参与了活动
  Future<bool> _checkUserParticipation() async {
    final currentUser = Services.auth.currentUser;
    if (currentUser == null) return false;

    return await Services.activity.isUserParticipant(
      _activity.id,
      currentUser.id,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('活动详情'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        actions: [
          // 分享按钮
          IconButton(onPressed: _shareActivity, icon: const Icon(Icons.share)),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(AppTheme.spacingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 活动基本信息
                    _buildActivityHeader(),
                    const SizedBox(height: AppTheme.spacingL),

                    // 活动描述
                    _buildActivityDescription(),
                    const SizedBox(height: AppTheme.spacingL),

                    // 活动信息
                    _buildActivityInfo(),
                    const SizedBox(height: AppTheme.spacingL),

                    // 参与者列表
                    _buildParticipantsList(),
                    const SizedBox(height: AppTheme.spacingL),

                    // 操作按钮
                    _buildActionButtons(),
                  ],
                ),
              ),
    );
  }

  /// 构建活动头部信息
  Widget _buildActivityHeader() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 活动标题
          Text(
            _activity.title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: AppTheme.spacingS),

          // 创建者信息
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundImage:
                    _activity.creatorAvatar != null
                        ? CachedNetworkImageProvider(_activity.creatorAvatar!)
                        : null,
                child:
                    _activity.creatorAvatar == null
                        ? Text(
                          _activity.creatorName
                                  ?.substring(0, 1)
                                  .toUpperCase() ??
                              '?',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                        : null,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _activity.creatorName ?? '未知用户',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    Text(
                      '发起人',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade500,
                      ),
                    ),
                  ],
                ),
              ),
              // 活动状态
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: _getStatusColor().withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: _getStatusColor().withOpacity(0.3)),
                ),
                child: Text(
                  _activity.statusDisplayText,
                  style: TextStyle(
                    fontSize: 12,
                    color: _getStatusColor(),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建活动描述
  Widget _buildActivityDescription() {
    if (_activity.description.isEmpty) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '活动描述',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: AppTheme.spacingS),
          Text(
            _activity.description,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建活动信息
  Widget _buildActivityInfo() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '活动信息',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: AppTheme.spacingM),

          // 时间信息
          _buildInfoRow(
            icon: Icons.access_time,
            title: '活动时间',
            content:
                '${_formatDateTime(_activity.startTime)} - ${_formatDateTime(_activity.endTime)}',
          ),
          const SizedBox(height: AppTheme.spacingS),

          // 地点信息
          if (_activity.locationName != null) ...[
            _buildInfoRow(
              icon: Icons.location_on,
              title: '活动地点',
              content: _activity.locationName!,
            ),
            const SizedBox(height: AppTheme.spacingS),
          ],

          // 参与人数
          _buildInfoRow(
            icon: Icons.group,
            title: '参与人数',
            content: '${_activity.currentParticipants}人参与',
          ),
        ],
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow({
    required IconData icon,
    required String title,
    required String content,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 20, color: AppTheme.primaryColor),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                content,
                style: TextStyle(fontSize: 13, color: Colors.grey.shade600),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建参与者列表
  Widget _buildParticipantsList() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                '参与者',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const Spacer(),
              Text(
                '${_participants.length}人',
                style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingM),

          // 参与者头像列表
          if (_participants.isNotEmpty)
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children:
                  _participants.map((participant) {
                    return Column(
                      children: [
                        CircleAvatar(
                          radius: 20,
                          backgroundImage:
                              participant.userAvatar != null
                                  ? CachedNetworkImageProvider(
                                    participant.userAvatar!,
                                  )
                                  : null,
                          child:
                              participant.userAvatar == null
                                  ? Text(
                                    participant.userName
                                            ?.substring(0, 1)
                                            .toUpperCase() ??
                                        '?',
                                    style: const TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  )
                                  : null,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          participant.userName ?? '未知',
                          style: const TextStyle(fontSize: 10),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    );
                  }).toList(),
            )
          else
            const Text(
              '暂无参与者',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    final currentUser = Services.auth.currentUser;
    final isCreator = currentUser?.id == _activity.creatorId;
    final canJoin =
        !_activity.isFull &&
        !_activity.isEnded &&
        !isCreator &&
        !_isUserParticipant;
    final canLeave = _isUserParticipant && !isCreator;

    return Column(
      children: [
        // 参与/退出按钮
        if (canJoin)
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isJoining ? null : _joinActivity,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.borderRadius),
                ),
              ),
              child:
                  _isJoining
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                      : const Text(
                        '参加活动',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
            ),
          ),

        if (canLeave)
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: _isJoining ? null : _leaveActivity,
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.red,
                side: const BorderSide(color: Colors.red),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.borderRadius),
                ),
              ),
              child: const Text(
                '退出活动',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ),
          ),

        // 群聊按钮
        if (_isUserParticipant && _groupChat != null) ...[
          const SizedBox(height: AppTheme.spacingS),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _openGroupChat,
              icon: const Icon(Icons.chat),
              label: const Text('进入群聊'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppTheme.primaryColor,
                side: BorderSide(color: AppTheme.primaryColor),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.borderRadius),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// 获取状态颜色
  Color _getStatusColor() {
    switch (_activity.status) {
      case ActivityStatus.active:
        if (_activity.isEnded) return Colors.grey;
        if (_activity.isStarted) return Colors.green;
        if (_activity.isFull) return Colors.orange;
        return AppTheme.primaryColor;
      case ActivityStatus.cancelled:
        return Colors.red;
      case ActivityStatus.completed:
        return Colors.grey;
    }
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime time) {
    return '${time.month}月${time.day}日 ${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  /// 参加活动
  Future<void> _joinActivity() async {
    setState(() => _isJoining = true);

    try {
      final success = await Services.activity.joinActivity(_activity.id);
      if (success && mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('参加活动成功！')));
        // 刷新活动详情
        await _loadActivityDetails();
      } else if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('参加活动失败，请重试')));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('参加活动失败: $e')));
      }
    } finally {
      if (mounted) {
        setState(() => _isJoining = false);
      }
    }
  }

  /// 退出活动
  Future<void> _leaveActivity() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('确认退出'),
            content: const Text('确定要退出这个活动吗？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('确定'),
              ),
            ],
          ),
    );

    if (confirmed != true) return;

    setState(() => _isJoining = true);

    try {
      final success = await Services.activity.leaveActivity(_activity.id);
      if (success && mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('已退出活动')));
        // 刷新活动详情
        await _loadActivityDetails();
      } else if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('退出活动失败，请重试')));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('退出活动失败: $e')));
      }
    } finally {
      if (mounted) {
        setState(() => _isJoining = false);
      }
    }
  }

  /// 打开群聊
  void _openGroupChat() {
    if (_groupChat == null) return;

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => GroupChatPage(groupChat: _groupChat!),
      ),
    );
  }

  /// 分享活动
  void _shareActivity() {
    // TODO: 实现活动分享功能
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('分享功能开发中...')));
  }
}
