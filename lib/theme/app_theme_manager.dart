import 'package:flutter/material.dart';
import 'app_theme_type.dart';
import 'theme_preferences.dart';
import 'material3_theme_data.dart';

/// 应用主题管理器
/// 
/// 负责管理应用的主题状态，包括主题类型、主题模式等
/// 使用ChangeNotifier实现状态管理，支持实时主题切换
class AppThemeManager extends ChangeNotifier {
  static AppThemeManager? _instance;
  
  /// 单例模式获取实例
  static AppThemeManager get instance {
    _instance ??= AppThemeManager._internal();
    return _instance!;
  }

  AppThemeManager._internal();

  // 私有变量
  AppThemeType _currentThemeType = AppThemeType.oceanBlue;
  ThemeMode _currentThemeMode = ThemeMode.system;
  bool _useSystemDynamicColor = false;
  bool _isInitialized = false;

  // 公共访问器
  AppThemeType get currentThemeType => _currentThemeType;
  ThemeMode get currentThemeMode => _currentThemeMode;
  bool get useSystemDynamicColor => _useSystemDynamicColor;
  bool get isInitialized => _isInitialized;

  /// 初始化主题管理器
  /// 从本地存储加载用户的主题偏好设置
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 并行加载所有设置
      final results = await Future.wait([
        ThemePreferences.getThemeType(),
        ThemePreferences.getThemeMode(),
        ThemePreferences.getUseSystemDynamicColor(),
      ]);

      _currentThemeType = results[0] as AppThemeType;
      _currentThemeMode = results[1] as ThemeMode;
      _useSystemDynamicColor = results[2] as bool;

      _isInitialized = true;
      
      debugPrint('主题管理器初始化完成: '
          'themeType=${_currentThemeType.key}, '
          'themeMode=${_currentThemeMode.name}, '
          'dynamicColor=$_useSystemDynamicColor');
      
      notifyListeners();
    } catch (e) {
      debugPrint('主题管理器初始化失败: $e');
      _isInitialized = true;
      notifyListeners();
    }
  }

  /// 设置主题类型
  Future<void> setThemeType(AppThemeType themeType) async {
    if (_currentThemeType == themeType) return;

    _currentThemeType = themeType;
    notifyListeners();

    try {
      await ThemePreferences.saveThemeType(themeType);
      debugPrint('主题类型已更改为: ${themeType.key}');
    } catch (e) {
      debugPrint('保存主题类型失败: $e');
    }
  }

  /// 设置主题模式
  Future<void> setThemeMode(ThemeMode themeMode) async {
    if (_currentThemeMode == themeMode) return;

    _currentThemeMode = themeMode;
    notifyListeners();

    try {
      await ThemePreferences.saveThemeMode(themeMode);
      debugPrint('主题模式已更改为: ${themeMode.name}');
    } catch (e) {
      debugPrint('保存主题模式失败: $e');
    }
  }

  /// 设置是否使用系统动态颜色
  Future<void> setUseSystemDynamicColor(bool useSystemDynamicColor) async {
    if (_useSystemDynamicColor == useSystemDynamicColor) return;

    _useSystemDynamicColor = useSystemDynamicColor;
    notifyListeners();

    try {
      await ThemePreferences.saveUseSystemDynamicColor(useSystemDynamicColor);
      debugPrint('动态颜色设置已更改为: $useSystemDynamicColor');
    } catch (e) {
      debugPrint('保存动态颜色设置失败: $e');
    }
  }

  /// 获取当前主题的浅色ColorScheme
  ColorScheme getLightColorScheme() {
    return Material3ThemeData.getLightColorScheme(_currentThemeType);
  }

  /// 获取当前主题的深色ColorScheme
  ColorScheme getDarkColorScheme() {
    return Material3ThemeData.getDarkColorScheme(_currentThemeType);
  }

  /// 获取当前主题的浅色ThemeData
  ThemeData getLightThemeData() {
    return Material3ThemeData.getLightThemeData(_currentThemeType);
  }

  /// 获取当前主题的深色ThemeData
  ThemeData getDarkThemeData() {
    return Material3ThemeData.getDarkThemeData(_currentThemeType);
  }

  /// 重置所有主题设置为默认值
  Future<void> resetToDefaults() async {
    _currentThemeType = AppThemeType.oceanBlue;
    _currentThemeMode = ThemeMode.system;
    _useSystemDynamicColor = false;
    
    notifyListeners();

    try {
      await ThemePreferences.clearAllThemeSettings();
      debugPrint('主题设置已重置为默认值');
    } catch (e) {
      debugPrint('重置主题设置失败: $e');
    }
  }

  /// 获取主题模式的显示名称
  String getThemeModeDisplayName(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return '浅色模式';
      case ThemeMode.dark:
        return '深色模式';
      case ThemeMode.system:
        return '跟随系统';
    }
  }

  /// 获取主题模式的描述
  String getThemeModeDescription(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return '始终使用浅色主题';
      case ThemeMode.dark:
        return '始终使用深色主题';
      case ThemeMode.system:
        return '根据系统设置自动切换';
    }
  }

  /// 获取主题模式的图标
  IconData getThemeModeIcon(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.brightness_auto;
    }
  }
}