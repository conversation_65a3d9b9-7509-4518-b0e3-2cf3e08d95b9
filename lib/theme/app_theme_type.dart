/// 应用主题类型枚举
/// 
/// 定义了应用支持的所有主题类型
enum AppThemeType {
  /// 海洋蓝调 - 深海蓝调，营造专业可靠的钓鱼氛围
  oceanBlue('oceanBlue', '海洋蓝调', 'Ocean Blue'),
  
  /// 自然绿意 - 贴近自然，体现户外钓鱼的生态感
  natureGreen('natureGreen', '自然绿意', 'Nature Green'),
  
  /// 日落暖调 - 黄昏钓鱼的温暖时光，活力而温馨
  sunsetWarm('sunsetWarm', '日落暖调', 'Sunset Warm'),
  
  /// 夜钓深邃 - 夜钓的神秘感，现代简约风格
  nightFishing('nightFishing', '夜钓深邃', 'Night Fishing'),
  
  /// 经典商务 - 经典商务蓝，专业可靠
  classicBusiness('classicBusiness', '经典商务', 'Classic Business');

  const AppThemeType(this.key, this.displayName, this.englishName);

  /// 主题键值，用于存储和识别
  final String key;
  
  /// 中文显示名称
  final String displayName;
  
  /// 英文名称
  final String englishName;

  /// 从字符串键值获取主题类型
  static AppThemeType fromKey(String key) {
    for (final theme in AppThemeType.values) {
      if (theme.key == key) {
        return theme;
      }
    }
    return AppThemeType.oceanBlue; // 默认主题
  }

  /// 获取主题描述
  String get description {
    switch (this) {
      case AppThemeType.oceanBlue:
        return '深海蓝调，营造专业可靠的钓鱼氛围，适合商务用户';
      case AppThemeType.natureGreen:
        return '贴近自然环境，体现户外钓鱼的生态感，适合环保意识用户';
      case AppThemeType.sunsetWarm:
        return '黄昏钓鱼的温暖时光，活力而温馨，适合年轻用户';
      case AppThemeType.nightFishing:
        return '夜钓的神秘感，现代简约风格，适合夜间使用';
      case AppThemeType.classicBusiness:
        return '经典商务蓝，专业可靠，适合企业用户';
    }
  }

  /// 获取主题图标
  String get iconEmoji {
    switch (this) {
      case AppThemeType.oceanBlue:
        return '🌊';
      case AppThemeType.natureGreen:
        return '🌿';
      case AppThemeType.sunsetWarm:
        return '🌅';
      case AppThemeType.nightFishing:
        return '🌙';
      case AppThemeType.classicBusiness:
        return '🎯';
    }
  }
}