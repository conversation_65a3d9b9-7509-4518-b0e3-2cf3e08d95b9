import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';

/// 天地图工具类
/// 统一管理天地图密钥和API调用
class TianDiTuUtils {
  /// 天地图密钥（统一管理）
  static const String apiKey = "23fee0544e2033fa1dbc0d6ae7bf30ad";
  
  /// 逆地理编码API基础URL
  static const String _geocodeBaseUrl = 'http://api.tianditu.gov.cn/geocoder';
  
  /// 地名搜索API基础URL
  static const String _searchBaseUrl = 'http://api.tianditu.gov.cn/v2/search';

  /// 获取天地图密钥
  /// 用于瓦片URL和API调用
  static String get key => apiKey;

  /// 逆地理编码：根据经纬度获取地址信息
  /// [longitude] 经度
  /// [latitude] 纬度
  /// 返回地址信息，如果失败返回null
  static Future<Map<String, dynamic>?> reverseGeocode(
    double longitude, 
    double latitude
  ) async {
    try {
      final String postStr = json.encode({
        'lon': longitude.toString(),
        'lat': latitude.toString(),
        'ver': '1'
      });
      
      final String url = '$_geocodeBaseUrl?postStr=$postStr&type=geocode&tk=$apiKey';
      
      debugPrint('天地图API请求: $url');
      
      final response = await http.get(Uri.parse(url)).timeout(
        const Duration(seconds: 10),
      );
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        
        debugPrint('天地图API响应: $data');
        
        if (data['status'] == '0') {
          return data['result'];
        } else {
          debugPrint('天地图API错误: ${data['msg']}');
          return null;
        }
      } else {
        debugPrint('HTTP请求失败: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('逆地理编码请求异常: $e');
      return null;
    }
  }

  /// 获取最佳的位置名称
  /// 优先级：POI > 道路 > 格式化地址的关键部分
  static Future<String?> getBestLocationName(
    double longitude, 
    double latitude
  ) async {
    try {
      final result = await reverseGeocode(longitude, latitude);
      if (result == null) return null;

      final addressComponent = result['addressComponent'];
      if (addressComponent == null) return null;

      // 优先使用POI名称
      String? poi = addressComponent['poi'];
      if (poi != null && poi.trim().isNotEmpty) {
        return poi.trim();
      }

      // 其次使用道路名称
      String? road = addressComponent['road'];
      if (road != null && road.trim().isNotEmpty) {
        return road.trim();
      }

      // 最后使用格式化地址的关键部分
      String? formattedAddress = result['formatted_address'];
      if (formattedAddress != null && formattedAddress.trim().isNotEmpty) {
        // 尝试提取地址中的关键部分
        List<String> parts = formattedAddress.split('');
        if (parts.isNotEmpty) {
          String lastPart = parts.last.trim();
          if (lastPart.isNotEmpty) {
            return lastPart;
          }
        }
        return formattedAddress.trim();
      }

      return null;
    } catch (e) {
      debugPrint('获取最佳位置名称失败: $e');
      return null;
    }
  }

  /// 获取格式化的地址字符串
  static Future<String?> getFormattedAddress(
    double longitude, 
    double latitude
  ) async {
    final result = await reverseGeocode(longitude, latitude);
    return result?['formatted_address'];
  }

  /// 获取道路名称
  static Future<String?> getRoadName(
    double longitude, 
    double latitude
  ) async {
    final result = await reverseGeocode(longitude, latitude);
    return result?['addressComponent']?['road'];
  }

  /// 获取POI信息
  static Future<String?> getPOIName(
    double longitude, 
    double latitude
  ) async {
    final result = await reverseGeocode(longitude, latitude);
    return result?['addressComponent']?['poi'];
  }

  /// 获取城市信息
  static Future<String?> getCityName(
    double longitude, 
    double latitude
  ) async {
    final result = await reverseGeocode(longitude, latitude);
    return result?['addressComponent']?['city'];
  }

  /// 构建瓦片URL模板
  /// [isVector] 是否为矢量图
  /// [isAnnotation] 是否为注记层
  /// 注意：返回的URL模板使用{k}占位符，需要通过additionalOptions传递密钥
  static String buildTileUrlTemplate({
    required bool isVector,
    required bool isAnnotation,
  }) {
    if (isAnnotation) {
      // 注记层
      return isVector
          ? "https://t{s}.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk={k}"
          : "https://t{s}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk={k}";
    } else {
      // 底图层
      return isVector
          ? "https://t{s}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk={k}"
          : "https://t{s}.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk={k}";
    }
  }

  /// 获取天地图子域名列表
  static List<String> get subdomains => const ['0', '1', '2', '3', '4', '5', '6', '7'];

  /// [*参数调整*]地名搜索：根据地名获取坐标信息
  /// [query] 搜索关键词
  /// [region] 搜索区域，可选参数，如"北京市"
  /// [queryType] 搜索类型：1-普通搜索，2-视野内搜索，3-周边搜索，4-建议词搜索，7-纯地名搜索
  /// [level] 地图级别 1-18
  /// [mapBound] 地图范围，格式："minLng,minLat,maxLng,maxLat"
  /// [centerPoint] 中心点坐标，用于周边搜索，格式："lng,lat"
  /// [radius] 搜索半径（米），用于周边搜索
  /// [count] 返回结果数量 1-300
  /// [start] 分页起始位置 0-300
  /// 返回搜索结果列表
  static Future<List<SearchResult>?> searchByName(
    String query, {
    String? region,
    int queryType = 1,
    int level = 18,
    String mapBound = '-180,-90,180,90',
    String? centerPoint,
    int? radius,
    int count = 20,
    int start = 0,
  }) async {
    try {
      // 构建postStr参数（JSON格式）- 支持完整的天地图搜索参数
      final Map<String, dynamic> postData = {
        'keyWord': query,
        'level': level,
        'mapBound': mapBound,
        'queryType': queryType,
        'count': count,
        'start': start,
      };

      // 可选参数
      if (region != null && region.isNotEmpty) {
        postData['specify'] = region;
      }
      
      // 周边搜索参数
      if (queryType == 3) {
        if (centerPoint != null) {
          postData['pointLonlat'] = centerPoint;
        }
        if (radius != null) {
          postData['queryRadius'] = radius.toString();
        }
      }
      
      // 纯地名搜索参数
      if (queryType == 7) {
        postData['queryTerminal'] = '10000';
      }

      // 构建请求URL - 使用正确的URL编码
      final postStr = json.encode(postData);
      final uri = Uri.parse(_searchBaseUrl).replace(queryParameters: {
        'postStr': postStr,
        'type': 'query',
        'tk': apiKey,
      });

      debugPrint('天地图搜索请求: $uri');

      // 添加重试机制
      http.Response? response;
      int retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          response = await http.get(uri).timeout(
            const Duration(seconds: 10),
          );
          break; // 成功则跳出循环
        } catch (e) {
          retryCount++;
          debugPrint('天地图搜索请求失败 (第$retryCount次): $e');
          if (retryCount >= maxRetries) {
            rethrow; // 达到最大重试次数，抛出异常
          }
          // 等待一段时间后重试
          await Future.delayed(Duration(milliseconds: 500 * retryCount));
        }
      }

      if (response == null) {
        throw Exception('网络请求失败，已达到最大重试次数');
      }
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        debugPrint('天地图搜索响应: $data');
        
        // 检查响应状态和结果类型
        final status = data['status'];
        final resultType = data['resultType'];
        
        // 建议词搜索没有status字段，直接检查suggests
        if (data['suggests'] != null && data['suggests'].isNotEmpty) {
          // 建议词响应
          final List<dynamic> suggests = data['suggests'];
          debugPrint('建议词搜索成功，返回 ${suggests.length} 条结果');
          return suggests.map((suggest) => SearchResult.fromSuggest(suggest)).toList();
        }
        // 其他类型的搜索检查status
        else if (status != null && status['infocode'] == 1000) {
          // 成功响应，检查结果类型
          if (resultType == 1 && data['pois'] != null) {
            // POI搜索结果
            final List<dynamic> pois = data['pois'];
            return pois.map((poi) => SearchResult.fromJson(poi)).toList();
          } else if (resultType == 2 && data['statistics'] != null) {
            // 统计结果 - 返回优先城市列表
            return _handleStatisticsResult(data);
          } else if (resultType == 3 && data['area'] != null) {
            // 行政区响应 - 单个区域对象
            final area = data['area'];
            return [SearchResult.fromArea(area)];
          } else {
            debugPrint('搜索成功但无结果，resultType: $resultType');
            return [];
          }
        } else {
          // 错误响应
          final errorMsg = status?['cndesc'] ?? '搜索失败';
          debugPrint('搜索失败: $errorMsg (infocode: ${status?['infocode']})');
          return [];
        }
      } else {
        debugPrint('搜索请求失败: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('地名搜索失败: $e');
      return null;
    }
  }

  /// 处理统计结果 - 返回优先城市列表
  static List<SearchResult> _handleStatisticsResult(Map<String, dynamic> data) {
    try {
      final statistics = data['statistics'];
      if (statistics != null && statistics['priorityCitys'] != null) {
        final List<dynamic> cities = statistics['priorityCitys'];
        debugPrint('处理统计结果，返回 ${cities.length} 个优先城市');
        
        return cities.map((city) {
          final lonlat = city['lonlat'] as String;
          final coords = lonlat.split(',');
          
          return SearchResult(
            name: city['adminName'] ?? '',
            address: '${city['adminName'] ?? ''} (${city['count']} 个相关地点)',
            longitude: double.tryParse(coords[0]) ?? 0.0,
            latitude: double.tryParse(coords[1]) ?? 0.0,
            category: '统计结果',
            province: null,
            city: city['adminName'],
            area: null,
            adminCode: city['adminCode']?.toString(),
          );
        }).toList();
      }
      return [];
    } catch (e) {
      debugPrint('处理统计结果失败: $e');
      return [];
    }
  }
}

/// 地址组件数据模型
class TianDiTuAddressComponent {
  final String? address;
  final int? addressDistance;
  final String? addressPosition;
  final String? city;
  final String? poi;
  final int? poiDistance;
  final String? poiPosition;
  final String? road;
  final int? roadDistance;

  TianDiTuAddressComponent({
    this.address,
    this.addressDistance,
    this.addressPosition,
    this.city,
    this.poi,
    this.poiDistance,
    this.poiPosition,
    this.road,
    this.roadDistance,
  });

  factory TianDiTuAddressComponent.fromJson(Map<String, dynamic> json) {
    return TianDiTuAddressComponent(
      address: json['address'],
      addressDistance: json['address_distance'],
      addressPosition: json['address_position'],
      city: json['city'],
      poi: json['poi'],
      poiDistance: json['poi_distance'],
      poiPosition: json['poi_position'],
      road: json['road'],
      roadDistance: json['road_distance'],
    );
  }

  @override
  String toString() {
    return 'TianDiTuAddressComponent{address: $address, city: $city, poi: $poi, road: $road}';
  }
}

/// 搜索结果数据模型
class SearchResult {
  final String name;          // 地点名称
  final String address;       // 详细地址
  final double longitude;     // 经度
  final double latitude;      // 纬度
  final String? category;     // 分类
  final String? province;     // 省份
  final String? city;         // 城市
  final String? area;         // 区域
  final String? adminCode;    // 行政区编码

  SearchResult({
    required this.name,
    required this.address,
    required this.longitude,
    required this.latitude,
    this.category,
    this.province,
    this.city,
    this.area,
    this.adminCode,
  });

  factory SearchResult.fromJson(Map<String, dynamic> json) {
    // 解析坐标点，格式通常是 "经度,纬度"
    final String lonlat = json['lonlat'] ?? '';
    final List<String> coords = lonlat.split(',');
    
    double longitude = 0.0;
    double latitude = 0.0;
    
    if (coords.length >= 2) {
      longitude = double.tryParse(coords[0]) ?? 0.0;
      latitude = double.tryParse(coords[1]) ?? 0.0;
    }

    return SearchResult(
      name: json['name'] ?? '',
      address: json['address'] ?? '',
      longitude: longitude,
      latitude: latitude,
      category: json['category'],
      province: json['province'],
      city: json['city'],
      area: json['area'],
      adminCode: json['adminCode']?.toString(),
    );
  }

  factory SearchResult.fromSuggest(Map<String, dynamic> json) {
    // 建议词响应格式
    final String lonlat = json['lonlat'] ?? '';
    final List<String> coords = lonlat.split(',');
    
    double longitude = 0.0;
    double latitude = 0.0;
    
    if (coords.length >= 2) {
      longitude = double.tryParse(coords[0]) ?? 0.0;
      latitude = double.tryParse(coords[1]) ?? 0.0;
    }
    
    // 调试坐标解析
    debugPrint('解析建议词坐标: lonlat="$lonlat" -> ($longitude, $latitude)');

    return SearchResult(
      name: json['name'] ?? '',
      address: json['address'] ?? '',
      longitude: longitude,
      latitude: latitude,
      category: '建议词',
      province: null,
      city: null,
      area: json['address'], // 建议词搜索中address字段包含完整地址
      adminCode: null,
    );
  }

  factory SearchResult.fromArea(Map<String, dynamic> json) {
    // 行政区响应格式
    final String lonlat = json['lonlat'] ?? '';
    final List<String> coords = lonlat.split(',');
    
    double longitude = 0.0;
    double latitude = 0.0;
    
    if (coords.length >= 2) {
      longitude = double.tryParse(coords[0]) ?? 0.0;
      latitude = double.tryParse(coords[1]) ?? 0.0;
    }

    return SearchResult(
      name: json['name'] ?? '',
      address: json['name'] ?? '',
      longitude: longitude,
      latitude: latitude,
      category: '行政区',
      province: null,
      city: null,
      area: json['name'],
      adminCode: json['adminCode']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'address': address,
      'longitude': longitude,
      'latitude': latitude,
      'category': category,
      'province': province,
      'city': city,
      'area': area,
      'adminCode': adminCode,
    };
  }

  @override
  String toString() {
    return 'SearchResult(name: $name, address: $address, coords: ($longitude, $latitude))';
  }
}
