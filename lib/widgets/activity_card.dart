import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../models/fishing_activity.dart';
import '../theme/unified_theme.dart';
import '../services/service_locator.dart';

/// 活动卡片组件
class ActivityCard extends StatelessWidget {
  /// 活动数据
  final FishingActivity activity;

  /// 点击回调
  final VoidCallback? onTap;

  /// 是否显示参与按钮
  final bool showJoinButton;

  /// 参与按钮回调
  final VoidCallback? onJoin;

  const ActivityCard({
    super.key,
    required this.activity,
    this.onTap,
    this.showJoinButton = true,
    this.onJoin,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadius),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 头部信息
              _buildHeader(),
              const SizedBox(height: AppTheme.spacingS),

              // 活动标题
              Text(
                activity.title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: AppTheme.spacingS),

              // 活动描述
              if (activity.description.isNotEmpty) ...[
                Text(
                  activity.description,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                    height: 1.4,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: AppTheme.spacingS),
              ],

              // 活动信息
              _buildActivityInfo(),
              const SizedBox(height: AppTheme.spacingS),

              // 底部操作栏
              if (showJoinButton) _buildActionBar(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建头部信息
  Widget _buildHeader() {
    return Row(
      children: [
        // 创建者头像
        CircleAvatar(
          radius: 16,
          backgroundImage:
              activity.creatorAvatar != null
                  ? CachedNetworkImageProvider(activity.creatorAvatar!)
                  : null,
          child:
              activity.creatorAvatar == null
                  ? Text(
                    activity.creatorName?.substring(0, 1).toUpperCase() ?? '?',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                  : null,
        ),
        const SizedBox(width: 8),

        // 创建者信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                activity.creatorName ?? '未知用户',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              Text(
                _formatTime(activity.created),
                style: TextStyle(fontSize: 12, color: Colors.grey.shade500),
              ),
            ],
          ),
        ),

        // 活动状态标签
        _buildStatusChip(),
      ],
    );
  }

  /// 构建活动信息
  Widget _buildActivityInfo() {
    return Column(
      children: [
        // 时间信息
        _buildInfoRow(
          icon: Icons.access_time,
          text:
              '${_formatDateTime(activity.startTime)} - ${_formatDateTime(activity.endTime)}',
        ),
        const SizedBox(height: 4),

        // 地点信息
        if (activity.locationName != null) ...[
          _buildInfoRow(icon: Icons.location_on, text: activity.locationName!),
          const SizedBox(height: 4),
        ],

        // 参与人数
        _buildInfoRow(
          icon: Icons.group,
          text: '${activity.currentParticipants}人参与',
        ),
      ],
    );
  }

  /// 构建信息行
  Widget _buildInfoRow({required IconData icon, required String text}) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey.shade600),
        const SizedBox(width: 6),
        Expanded(
          child: Text(
            text,
            style: TextStyle(fontSize: 13, color: Colors.grey.shade600),
          ),
        ),
      ],
    );
  }

  /// 构建状态标签
  Widget _buildStatusChip() {
    Color chipColor;
    String statusText = activity.statusDisplayText;

    switch (activity.status) {
      case ActivityStatus.active:
        if (activity.isEnded) {
          chipColor = Colors.grey;
        } else if (activity.isStarted) {
          chipColor = Colors.green;
        } else if (activity.isFull) {
          chipColor = Colors.orange;
        } else {
          chipColor = AppTheme.primaryColor;
        }
        break;
      case ActivityStatus.cancelled:
        chipColor = Colors.red;
        break;
      case ActivityStatus.completed:
        chipColor = Colors.grey;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: chipColor.withOpacity(0.3)),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          fontSize: 12,
          color: chipColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 构建操作栏
  Widget _buildActionBar() {
    final currentUser = Services.auth.currentUser;
    final isCreator = currentUser?.id == activity.creatorId;
    final canJoin = !activity.isFull && !activity.isEnded && !isCreator;

    return Row(
      children: [
        // 参与者头像列表（显示前3个）
        Expanded(
          child: Row(
            children: [
              // TODO: 显示参与者头像
              Text(
                '${activity.currentParticipants}人参与',
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              ),
            ],
          ),
        ),

        // 参与按钮
        if (canJoin && onJoin != null)
          ElevatedButton(
            onPressed: onJoin,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            child: const Text('参与', style: TextStyle(fontSize: 12)),
          ),

        // 创建者标识
        if (isCreator)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Text(
              '我发起的',
              style: TextStyle(
                fontSize: 12,
                color: Colors.blue,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }

  /// 格式化时间
  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${time.month}-${time.day}';
    }
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime time) {
    return '${time.month}/${time.day} ${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}
