import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

/// 添加选择弹窗
/// 
/// 用户点击添加按钮后显示的选择界面
/// 可以选择发布钓点或创建一起钓鱼活动
class AddOptionDialog extends StatelessWidget {
  /// 选择发布钓点的回调
  final VoidCallback onAddSpot;

  /// 选择一起钓鱼的回调
  final VoidCallback onCreateActivity;

  const AddOptionDialog({
    super.key,
    required this.onAddSpot,
    required this.onCreateActivity,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            const Text(
              '选择发布内容',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 24),

            // 发布钓点选项
            _buildOptionCard(
              context: context,
              icon: FontAwesomeIcons.locationDot,
              iconColor: const Color(0xFF4A90E2),
              title: '发布钓点',
              subtitle: '分享优质钓点位置',
              onTap: () {
                Navigator.of(context).pop();
                onAddSpot();
              },
            ),

            const SizedBox(height: 16),

            // 一起钓鱼选项
            _buildOptionCard(
              context: context,
              icon: FontAwesomeIcons.userGroup,
              iconColor: const Color(0xFF52C41A),
              title: '一起钓鱼',
              subtitle: '发起钓鱼活动，邀请钓友',
              onTap: () {
                Navigator.of(context).pop();
                onCreateActivity();
              },
            ),

            const SizedBox(height: 24),

            // 取消按钮
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                '取消',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建选项卡片
  Widget _buildOptionCard({
    required BuildContext context,
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(
            color: Colors.grey.shade300,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            // 图标
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: iconColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: iconColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),

            // 文本内容
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),

            // 箭头图标
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey.shade400,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// 显示添加选择弹窗
  static Future<void> show({
    required BuildContext context,
    required VoidCallback onAddSpot,
    required VoidCallback onCreateActivity,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (context) => AddOptionDialog(
        onAddSpot: onAddSpot,
        onCreateActivity: onCreateActivity,
      ),
    );
  }
}
