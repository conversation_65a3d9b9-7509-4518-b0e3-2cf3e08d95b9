import 'dart:async';
import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';

import '../models/fishing_activity.dart';
import '../theme/unified_theme.dart';
import '../services/service_locator.dart';
import '../utils/tianditu_utils.dart';
import 'add_spot_form/image_upload_widget.dart';
import 'add_spot_form/image_upload_manager.dart';

/// 分屏创建活动组件
class SplitScreenActivityCreate extends StatefulWidget {
  /// 活动位置
  final LatLng location;

  /// 位置更新回调
  final Function(LatLng) onLocationChanged;

  /// 关闭回调
  final VoidCallback onClose;

  /// 成功创建活动回调
  final Function(FishingActivity) onActivityCreated;

  /// 建议的活动标题
  final String? suggestedTitle;

  const SplitScreenActivityCreate({
    super.key,
    required this.location,
    required this.onLocationChanged,
    required this.onClose,
    required this.onActivityCreated,
    this.suggestedTitle,
  });

  @override
  State<SplitScreenActivityCreate> createState() =>
      _SplitScreenActivityCreateState();
}

class _SplitScreenActivityCreateState extends State<SplitScreenActivityCreate> {
  final _formKey = GlobalKey<FormState>();
  final _imageUploadManager = ImageUploadManager();

  // 表单控制器
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationNameController = TextEditingController();

  // 表单状态
  final List<ImageUploadItem> _selectedImages = [];
  DateTime? _startTime;
  DateTime? _endTime;

  // UI状态
  bool _isSubmitting = false;
  bool _isRefreshingLocationName = false;

  @override
  void initState() {
    super.initState();
    // 如果有建议的标题，设置到标题字段中
    if (widget.suggestedTitle != null &&
        widget.suggestedTitle!.trim().isNotEmpty) {
      _titleController.text = widget.suggestedTitle!;
    }

    // 自动获取地名
    _refreshLocationName();
  }

  @override
  void didUpdateWidget(SplitScreenActivityCreate oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 当位置更新时，刷新地名
    if (widget.location != oldWidget.location) {
      _refreshLocationName();
    }

    // 当建议标题更新时，如果输入框为空，则填充新的建议标题
    if (widget.suggestedTitle != oldWidget.suggestedTitle &&
        widget.suggestedTitle != null &&
        widget.suggestedTitle!.trim().isNotEmpty &&
        _titleController.text.trim().isEmpty) {
      _titleController.text = widget.suggestedTitle!;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _locationNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.75, // 占屏幕75%高度
      decoration: const BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppTheme.radiusL),
        ),
      ),
      child: Column(
        children: [
          // 顶部拖拽指示器
          _buildDragIndicator(),

          // 标题栏
          _buildHeader(),

          // 表单内容
          Expanded(child: _buildForm()),
        ],
      ),
    );
  }

  /// 构建拖拽指示器
  Widget _buildDragIndicator() {
    return Container(
      margin: const EdgeInsets.only(top: AppTheme.spacingS),
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: AppTheme.borderColor,
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  /// 构建标题栏
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: const BoxDecoration(
        border: Border(bottom: BorderSide(color: AppTheme.borderColor)),
      ),
      child: Row(
        children: [
          const Text('发起一起钓鱼', style: AppTheme.headlineMedium),
          const Spacer(),
          TextButton(
            onPressed: _canSubmit() ? _submitForm : null,
            child: Text(
              _imageUploadManager.hasUploadingImages(_selectedImages)
                  ? _imageUploadManager.getUploadStatusText(_selectedImages)
                  : '发布',
              style: AppTheme.labelLarge.copyWith(
                color:
                    _canSubmit()
                        ? AppTheme.designPrimaryColor
                        : AppTheme.textHintColor,
              ),
            ),
          ),
          IconButton(onPressed: widget.onClose, icon: const Icon(Icons.close)),
        ],
      ),
    );
  }

  /// 构建表单
  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppTheme.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 活动标题
            _buildFormCard(child: _buildTitleInput()),

            const SizedBox(height: AppTheme.spacingM),

            // 图片上传
            _buildFormCard(
              child: ImageUploadWidget(
                selectedImages: _selectedImages,
                onImagesAdded: _handleImagesAdded,
                onImageRemoved: _handleImageRemoved,
              ),
            ),

            const SizedBox(height: AppTheme.spacingM),

            // 活动描述
            _buildFormCard(child: _buildDescriptionInput()),

            const SizedBox(height: AppTheme.spacingM),

            // 活动地点
            _buildFormCard(child: _buildLocationInput()),

            const SizedBox(height: AppTheme.spacingM),

            // 活动时间
            _buildFormCard(child: _buildTimeSelector()),
          ],
        ),
      ),
    );
  }

  /// 刷新位置名称
  Future<void> _refreshLocationName() async {
    if (_isRefreshingLocationName) return;

    setState(() {
      _isRefreshingLocationName = true;
    });

    try {
      final result = await TianDiTuUtils.reverseGeocode(
        widget.location.longitude,
        widget.location.latitude,
      );

      if (result != null && mounted) {
        final locationName = result['formatted_address'] ?? result['address'];
        if (locationName != null) {
          _locationNameController.text = locationName;

          // 如果标题为空，自动填充标题
          if (_titleController.text.isEmpty) {
            _titleController.text = '$locationName 钓鱼活动';
          }
        }
      }
    } catch (e) {
      debugPrint('❌ [活动创建] 获取位置名称失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshingLocationName = false;
        });
      }
    }
  }

  /// 处理图片添加
  void _handleImagesAdded(List<ImageUploadItem> newImages) {
    setState(() {
      _selectedImages.addAll(newImages);
    });

    // 立即开始上传图片
    _imageUploadManager.uploadSelectedImages(newImages, (updatedItem) {
      setState(() {
        // 更新图片状态
      });
    });
  }

  /// 处理图片移除
  void _handleImageRemoved(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  /// 检查是否可以提交
  bool _canSubmit() {
    return !_isSubmitting &&
        !_imageUploadManager.hasUploadingImages(_selectedImages);
  }

  /// 构建表单卡片
  Widget _buildFormCard({required Widget child}) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: child,
    );
  }

  /// 构建标题输入组件
  Widget _buildTitleInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('活动标题', style: AppTheme.headlineSmall),
        const SizedBox(height: AppTheme.spacingS),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: '活动标题 *',
                  hintText: '给你的钓鱼活动起个名字',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入活动标题';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: AppTheme.spacingS),
            IconButton(
              onPressed:
                  _isRefreshingLocationName ? null : _refreshLocationName,
              icon:
                  _isRefreshingLocationName
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Icon(Icons.refresh),
              tooltip: '根据位置自动填充标题',
            ),
          ],
        ),
      ],
    );
  }

  /// 构建描述输入组件
  Widget _buildDescriptionInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('活动描述', style: AppTheme.headlineSmall),
        const SizedBox(height: AppTheme.spacingS),
        TextFormField(
          controller: _descriptionController,
          maxLines: 4,
          decoration: const InputDecoration(
            labelText: '活动描述 *',
            hintText: '描述一下这次钓鱼活动的详情...',
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入活动描述';
            }
            return null;
          },
        ),
      ],
    );
  }

  /// 构建位置输入组件
  Widget _buildLocationInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('活动地点', style: AppTheme.headlineSmall),
        const SizedBox(height: AppTheme.spacingS),
        TextFormField(
          controller: _locationNameController,
          decoration: const InputDecoration(
            labelText: '活动地点 *',
            hintText: '输入具体地点名称',
            border: OutlineInputBorder(),
            suffixIcon: Icon(Icons.location_on),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入活动地点';
            }
            return null;
          },
        ),
        const SizedBox(height: AppTheme.spacingS),
        Text(
          '坐标: ${widget.location.latitude.toStringAsFixed(6)}, ${widget.location.longitude.toStringAsFixed(6)}',
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
      ],
    );
  }

  /// 构建时间选择组件
  Widget _buildTimeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('活动时间', style: AppTheme.headlineSmall),
        const SizedBox(height: AppTheme.spacingS),

        // 开始时间
        ListTile(
          contentPadding: EdgeInsets.zero,
          leading: const Icon(Icons.access_time),
          title: const Text('开始时间'),
          subtitle: Text(
            _startTime != null
                ? '${_startTime!.year}-${_startTime!.month.toString().padLeft(2, '0')}-${_startTime!.day.toString().padLeft(2, '0')} ${_startTime!.hour.toString().padLeft(2, '0')}:${_startTime!.minute.toString().padLeft(2, '0')}'
                : '请选择开始时间',
          ),
          onTap: () => _selectDateTime(true),
        ),
        const Divider(),

        // 结束时间
        ListTile(
          contentPadding: EdgeInsets.zero,
          leading: const Icon(Icons.access_time_filled),
          title: const Text('结束时间'),
          subtitle: Text(
            _endTime != null
                ? '${_endTime!.year}-${_endTime!.month.toString().padLeft(2, '0')}-${_endTime!.day.toString().padLeft(2, '0')} ${_endTime!.hour.toString().padLeft(2, '0')}:${_endTime!.minute.toString().padLeft(2, '0')}'
                : '请选择结束时间',
          ),
          onTap: () => _selectDateTime(false),
        ),
      ],
    );
  }

  /// 选择日期时间
  Future<void> _selectDateTime(bool isStartTime) async {
    final now = DateTime.now();
    final initialDate =
        isStartTime
            ? (_startTime ?? now.add(const Duration(hours: 1)))
            : (_endTime ??
                _startTime?.add(const Duration(hours: 2)) ??
                now.add(const Duration(hours: 3)));

    final date = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: now,
      lastDate: now.add(const Duration(days: 365)),
    );

    if (date != null && mounted) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(initialDate),
      );

      if (time != null && mounted) {
        final selectedDateTime = DateTime(
          date.year,
          date.month,
          date.day,
          time.hour,
          time.minute,
        );

        setState(() {
          if (isStartTime) {
            _startTime = selectedDateTime;
            // 如果结束时间早于开始时间，自动调整
            if (_endTime != null && _endTime!.isBefore(selectedDateTime)) {
              _endTime = selectedDateTime.add(const Duration(hours: 2));
            }
          } else {
            _endTime = selectedDateTime;
          }
        });
      }
    }
  }

  /// 提交表单
  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_startTime == null || _endTime == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请选择活动时间')));
      return;
    }

    if (_endTime!.isBefore(_startTime!)) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('结束时间不能早于开始时间')));
      return;
    }

    setState(() => _isSubmitting = true);

    try {
      // 创建活动记录
      final activityId = await _createActivityRecord();

      // 关联已上传的图片
      if (_selectedImages.isNotEmpty) {
        final uploadedImages =
            _selectedImages.where((item) => item.isCompleted).toList();
        if (uploadedImages.isNotEmpty) {
          await _imageUploadManager.associateUploadedImages(
            activityId,
            Services.auth.currentUser!.id,
            uploadedImages,
          );
        }
      }

      // 获取创建的活动并回调
      final activity = await Services.activity.getActivityById(activityId);
      if (activity != null && mounted) {
        widget.onActivityCreated(activity);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('创建活动失败: $e')));
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  /// 创建活动记录
  Future<String> _createActivityRecord() async {
    final activity = await Services.activity.createActivity(
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim(),
      location: widget.location,
      locationName: _locationNameController.text.trim(),
      startTime: _startTime!,
      endTime: _endTime!,
    );

    if (activity == null) {
      throw Exception('创建活动失败');
    }

    return activity.id;
  }
}
