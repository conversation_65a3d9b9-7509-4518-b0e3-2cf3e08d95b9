import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 可访问性增强组件集合
///
/// 功能：
/// - 语义标签增强
/// - 键盘导航支持
/// - 屏幕阅读器优化
/// - 焦点管理

/// 可访问性增强的表单字段
class AccessibleFormField extends StatelessWidget {
  final Widget child;
  final String label;
  final String? hint;
  final String? error;
  final bool required;
  final VoidCallback? onTap;

  const AccessibleFormField({
    super.key,
    required this.child,
    required this.label,
    this.hint,
    this.error,
    this.required = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    String semanticsLabel = label;
    if (required) {
      semanticsLabel += '，必填';
    }
    if (hint != null) {
      semanticsLabel += '，$hint';
    }
    if (error != null) {
      semanticsLabel += '，错误：$error';
    }

    return Semantics(
      label: semanticsLabel,
      hint: hint,
      onTap: onTap,
      child: child,
    );
  }
}

/// 可访问性增强的选择器
class AccessibleSelector extends StatelessWidget {
  final List<String> options;
  final String selectedValue;
  final ValueChanged<String> onChanged;
  final String label;
  final String? hint;

  const AccessibleSelector({
    super.key,
    required this.options,
    required this.selectedValue,
    required this.onChanged,
    required this.label,
    this.hint,
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: '$label，当前选择：$selectedValue',
      hint: hint ?? '双击可更改选择',
      child: Focus(
        child: Builder(
          builder: (context) {
            final focusNode = Focus.of(context);
            return GestureDetector(
              onTap: () => _showSelectionDialog(context),
              child: Container(
                decoration: BoxDecoration(
                  border: focusNode.hasFocus 
                      ? Border.all(color: Theme.of(context).primaryColor, width: 2)
                      : null,
                ),
                child: _buildSelectorContent(),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildSelectorContent() {
    // 这里应该返回实际的选择器UI
    return Container(); // 占位符
  }

  void _showSelectionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(label),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: options.map((option) {
            return Semantics(
              button: true,
              label: option,
              hint: selectedValue == option ? '当前选中' : '点击选择',
              child: ListTile(
                title: Text(option),
                selected: selectedValue == option,
                onTap: () {
                  onChanged(option);
                  Navigator.of(context).pop();
                },
              ),
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }
}

/// 可访问性增强的图片上传区域
class AccessibleImageUpload extends StatelessWidget {
  final List<String> imageUrls;
  final VoidCallback onAddImage;
  final Function(int) onRemoveImage;

  const AccessibleImageUpload({
    super.key,
    required this.imageUrls,
    required this.onAddImage,
    required this.onRemoveImage,
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: '图片上传区域，已上传${imageUrls.length}张图片',
      hint: '可以添加或删除图片',
      child: Column(
        children: [
          // 已上传的图片列表
          if (imageUrls.isNotEmpty)
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: imageUrls.asMap().entries.map((entry) {
                final index = entry.key;
                final url = entry.value;
                return _buildImageItem(context, url, index);
              }).toList(),
            ),
          
          // 添加图片按钮
          Semantics(
            button: true,
            label: '添加图片',
            hint: '点击选择图片上传',
            child: GestureDetector(
              onTap: onAddImage,
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.add_photo_alternate),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageItem(BuildContext context, String url, int index) {
    return Semantics(
      image: true,
      label: '第${index + 1}张图片',
      hint: '双击删除',
      child: Stack(
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: DecorationImage(
                image: NetworkImage(url),
                fit: BoxFit.cover,
              ),
            ),
          ),
          Positioned(
            top: 4,
            right: 4,
            child: Semantics(
              button: true,
              label: '删除第${index + 1}张图片',
              child: GestureDetector(
                onTap: () => onRemoveImage(index),
                child: Container(
                  width: 20,
                  height: 20,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    size: 16,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 可访问性增强的状态指示器
class AccessibleStatusIndicator extends StatelessWidget {
  final bool isActive;
  final String activeLabel;
  final String inactiveLabel;
  final IconData icon;
  final Color? activeColor;
  final Color? inactiveColor;

  const AccessibleStatusIndicator({
    super.key,
    required this.isActive,
    required this.activeLabel,
    required this.inactiveLabel,
    required this.icon,
    this.activeColor,
    this.inactiveColor,
  });

  @override
  Widget build(BuildContext context) {
    final label = isActive ? activeLabel : inactiveLabel;
    final color = isActive 
        ? (activeColor ?? Colors.green)
        : (inactiveColor ?? Colors.grey);

    return Semantics(
      label: label,
      value: isActive ? '已激活' : '未激活',
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: color,
            semanticLabel: label,
          ),
          const SizedBox(width: 8),
          Text(
            label,
            style: TextStyle(color: color),
          ),
        ],
      ),
    );
  }
}

/// 焦点管理器
class FocusManager extends StatefulWidget {
  final List<Widget> children;
  final Axis direction;

  const FocusManager({
    super.key,
    required this.children,
    this.direction = Axis.vertical,
  });

  @override
  State<FocusManager> createState() => _FocusManagerState();
}

class _FocusManagerState extends State<FocusManager> {
  late List<FocusNode> _focusNodes;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _focusNodes = List.generate(
      widget.children.length,
      (index) => FocusNode(),
    );
  }

  @override
  void dispose() {
    for (final node in _focusNodes) {
      node.dispose();
    }
    super.dispose();
  }

  void _moveFocus(int direction) {
    final newIndex = (_currentIndex + direction).clamp(0, _focusNodes.length - 1);
    if (newIndex != _currentIndex) {
      _currentIndex = newIndex;
      _focusNodes[_currentIndex].requestFocus();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      onKeyEvent: (node, event) {
        if (event is KeyDownEvent) {
          if (widget.direction == Axis.vertical) {
            if (event.logicalKey.keyLabel == 'Arrow Down') {
              _moveFocus(1);
              return KeyEventResult.handled;
            } else if (event.logicalKey.keyLabel == 'Arrow Up') {
              _moveFocus(-1);
              return KeyEventResult.handled;
            }
          } else {
            if (event.logicalKey.keyLabel == 'Arrow Right') {
              _moveFocus(1);
              return KeyEventResult.handled;
            } else if (event.logicalKey.keyLabel == 'Arrow Left') {
              _moveFocus(-1);
              return KeyEventResult.handled;
            }
          }
        }
        return KeyEventResult.ignored;
      },
      child: Column(
        children: widget.children.asMap().entries.map((entry) {
          final index = entry.key;
          final child = entry.value;
          return Focus(
            focusNode: _focusNodes[index],
            child: child,
          );
        }).toList(),
      ),
    );
  }
}
