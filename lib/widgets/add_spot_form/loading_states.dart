import 'package:flutter/material.dart';
import '../../theme/unified_theme.dart';

/// 加载状态组件集合
///
/// 功能：
/// - 统一的加载状态UI
/// - 骨架屏效果
/// - 错误状态处理
/// - 空状态展示

/// 骨架屏加载组件
class SkeletonLoader extends StatefulWidget {
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;

  const SkeletonLoader({super.key, this.width, this.height, this.borderRadius});

  @override
  State<SkeletonLoader> createState() => _SkeletonLoaderState();
}

class _SkeletonLoaderState extends State<SkeletonLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                AppTheme.surfaceColor,
                AppTheme.surfaceColor.withValues(alpha: 0.5),
                AppTheme.surfaceColor,
              ],
              stops: [0.0, _animation.value, 1.0],
            ),
          ),
        );
      },
    );
  }
}

/// 表单字段骨架屏
class FormFieldSkeleton extends StatelessWidget {
  final String? label;
  final double height;

  const FormFieldSkeleton({super.key, this.label, this.height = 56});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null) ...[
          SkeletonLoader(
            width: 80,
            height: 16,
            borderRadius: BorderRadius.circular(4),
          ),
          const SizedBox(height: AppTheme.spacingS),
        ],
        SkeletonLoader(
          width: double.infinity,
          height: height,
          borderRadius: BorderRadius.circular(AppTheme.radiusM),
        ),
      ],
    );
  }
}

/// 图片上传区域骨架屏
class ImageUploadSkeleton extends StatelessWidget {
  const ImageUploadSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SkeletonLoader(
          width: 60,
          height: 16,
          borderRadius: BorderRadius.circular(4),
        ),
        const SizedBox(height: AppTheme.spacingS),
        Row(
          children: [
            SkeletonLoader(
              width: 80,
              height: 80,
              borderRadius: BorderRadius.circular(AppTheme.radiusM),
            ),
            const SizedBox(width: AppTheme.spacingS),
            SkeletonLoader(
              width: 80,
              height: 80,
              borderRadius: BorderRadius.circular(AppTheme.radiusM),
            ),
            const SizedBox(width: AppTheme.spacingS),
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                border: Border.all(
                  color: AppTheme.borderColor,
                  style: BorderStyle.solid,
                ),
                borderRadius: BorderRadius.circular(AppTheme.radiusM),
              ),
              child: const Icon(
                Icons.add_photo_alternate_outlined,
                color: AppTheme.textHintColor,
                size: 32,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

/// 加载状态指示器
class LoadingIndicator extends StatelessWidget {
  final String? message;
  final double size;

  const LoadingIndicator({super.key, this.message, this.size = 24});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              AppTheme.designPrimaryColor,
            ),
          ),
        ),
        if (message != null) ...[
          const SizedBox(height: AppTheme.spacingS),
          Text(
            message!,
            style: AppTheme.bodySmall.copyWith(color: AppTheme.textHintColor),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

/// 错误状态组件
class ErrorState extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;
  final IconData icon;

  const ErrorState({
    super.key,
    required this.message,
    this.onRetry,
    this.icon = Icons.error_outline,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 48, color: AppTheme.errorColor),
            const SizedBox(height: AppTheme.spacingM),
            Text(
              message,
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: AppTheme.spacingM),
              ElevatedButton(
                onPressed: onRetry,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.designPrimaryColor,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppTheme.radiusM),
                  ),
                ),
                child: const Text('重试'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// 空状态组件
class EmptyState extends StatelessWidget {
  final String message;
  final String? actionText;
  final VoidCallback? onAction;
  final IconData icon;

  const EmptyState({
    super.key,
    required this.message,
    this.actionText,
    this.onAction,
    this.icon = Icons.inbox_outlined,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 48, color: AppTheme.textHintColor),
            const SizedBox(height: AppTheme.spacingM),
            Text(
              message,
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            if (actionText != null && onAction != null) ...[
              const SizedBox(height: AppTheme.spacingM),
              ElevatedButton(
                onPressed: onAction,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.designPrimaryColor,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppTheme.radiusM),
                  ),
                ),
                child: Text(actionText!),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// 进度指示器
class ProgressIndicator extends StatelessWidget {
  final double progress;
  final String? label;
  final Color? color;

  const ProgressIndicator({
    super.key,
    required this.progress,
    this.label,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (label != null) ...[
          Text(
            label!,
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingS),
        ],
        LinearProgressIndicator(
          value: progress,
          backgroundColor: AppTheme.surfaceColor,
          valueColor: AlwaysStoppedAnimation<Color>(
            color ?? AppTheme.designPrimaryColor,
          ),
        ),
      ],
    );
  }
}
