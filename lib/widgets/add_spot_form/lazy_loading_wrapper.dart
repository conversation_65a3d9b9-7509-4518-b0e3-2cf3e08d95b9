import 'package:flutter/material.dart';
import 'loading_states.dart';

/// 懒加载包装器
///
/// 功能：
/// - 延迟加载组件
/// - 可视区域检测
/// - 内存优化
/// - 渐进式渲染

/// 懒加载组件包装器
class LazyLoadingWrapper extends StatefulWidget {
  /// 要懒加载的组件构建器
  final Widget Function() builder;
  
  /// 占位符组件
  final Widget? placeholder;
  
  /// 是否启用懒加载
  final bool enabled;
  
  /// 预加载距离（像素）
  final double preloadDistance;
  
  /// 加载延迟
  final Duration delay;

  const LazyLoadingWrapper({
    super.key,
    required this.builder,
    this.placeholder,
    this.enabled = true,
    this.preloadDistance = 200,
    this.delay = const Duration(milliseconds: 100),
  });

  @override
  State<LazyLoadingWrapper> createState() => _LazyLoadingWrapperState();
}

class _LazyLoadingWrapperState extends State<LazyLoadingWrapper> {
  bool _isVisible = false;
  bool _isLoaded = false;
  Widget? _cachedWidget;

  @override
  void initState() {
    super.initState();
    if (!widget.enabled) {
      _isLoaded = true;
      _cachedWidget = widget.builder();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enabled) {
      return _cachedWidget!;
    }

    return VisibilityDetector(
      key: widget.key ?? UniqueKey(),
      onVisibilityChanged: _onVisibilityChanged,
      child: _isLoaded 
          ? (_cachedWidget ?? widget.builder())
          : (widget.placeholder ?? _buildDefaultPlaceholder()),
    );
  }

  void _onVisibilityChanged(VisibilityInfo info) {
    if (!_isLoaded && info.visibleFraction > 0) {
      setState(() {
        _isVisible = true;
      });
      
      Future.delayed(widget.delay, () {
        if (mounted && _isVisible) {
          setState(() {
            _isLoaded = true;
            _cachedWidget = widget.builder();
          });
        }
      });
    }
  }

  Widget _buildDefaultPlaceholder() {
    return const SizedBox(
      height: 100,
      child: Center(
        child: LoadingIndicator(message: '加载中...'),
      ),
    );
  }
}

/// 可视区域检测器
class VisibilityDetector extends StatefulWidget {
  final Widget child;
  final Function(VisibilityInfo) onVisibilityChanged;

  const VisibilityDetector({
    super.key,
    required this.child,
    required this.onVisibilityChanged,
  });

  @override
  State<VisibilityDetector> createState() => _VisibilityDetectorState();
}

class _VisibilityDetectorState extends State<VisibilityDetector> {
  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      onNotification: (notification) {
        _checkVisibility();
        return false;
      },
      child: widget.child,
    );
  }

  void _checkVisibility() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      
      final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
      if (renderBox == null) return;

      final size = renderBox.size;
      final position = renderBox.localToGlobal(Offset.zero);
      final screenSize = MediaQuery.of(context).size;

      final visibleHeight = _calculateVisibleHeight(
        position.dy,
        size.height,
        screenSize.height,
      );

      final visibleFraction = visibleHeight / size.height;

      widget.onVisibilityChanged(VisibilityInfo(
        key: widget.key,
        size: size,
        visibleFraction: visibleFraction.clamp(0.0, 1.0),
      ));
    });
  }

  double _calculateVisibleHeight(double top, double height, double screenHeight) {
    if (top >= screenHeight || top + height <= 0) {
      return 0;
    }
    
    final visibleTop = top < 0 ? 0 : top;
    final visibleBottom = top + height > screenHeight ? screenHeight : top + height;
    
    return visibleBottom - visibleTop;
  }
}

/// 可视性信息
class VisibilityInfo {
  final Key? key;
  final Size size;
  final double visibleFraction;

  VisibilityInfo({
    this.key,
    required this.size,
    required this.visibleFraction,
  });
}

/// 渐进式图片加载器
class ProgressiveImageLoader extends StatefulWidget {
  final String imageUrl;
  final String? thumbnailUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;

  const ProgressiveImageLoader({
    super.key,
    required this.imageUrl,
    this.thumbnailUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
  });

  @override
  State<ProgressiveImageLoader> createState() => _ProgressiveImageLoaderState();
}

class _ProgressiveImageLoaderState extends State<ProgressiveImageLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  
  bool _thumbnailLoaded = false;
  bool _fullImageLoaded = false;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: Stack(
        fit: StackFit.expand,
        children: [
          // 占位符
          if (!_thumbnailLoaded && !_fullImageLoaded && !_hasError)
            widget.placeholder ?? _buildDefaultPlaceholder(),

          // 缩略图
          if (widget.thumbnailUrl != null && !_fullImageLoaded)
            Image.network(
              widget.thumbnailUrl!,
              fit: widget.fit,
              frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
                if (frame != null && !_thumbnailLoaded) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (mounted) {
                      setState(() => _thumbnailLoaded = true);
                    }
                  });
                }
                return child;
              },
              errorBuilder: (context, error, stackTrace) {
                return Container();
              },
            ),

          // 完整图片
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Opacity(
                opacity: _animation.value,
                child: Image.network(
                  widget.imageUrl,
                  fit: widget.fit,
                  frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
                    if (frame != null && !_fullImageLoaded) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        if (mounted) {
                          setState(() => _fullImageLoaded = true);
                          _animationController.forward();
                        }
                      });
                    }
                    return child;
                  },
                  errorBuilder: (context, error, stackTrace) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        setState(() => _hasError = true);
                      }
                    });
                    return widget.errorWidget ?? _buildDefaultError();
                  },
                ),
              );
            },
          ),

          // 错误状态
          if (_hasError && !_fullImageLoaded)
            widget.errorWidget ?? _buildDefaultError(),
        ],
      ),
    );
  }

  Widget _buildDefaultPlaceholder() {
    return Container(
      color: Colors.grey[200],
      child: const Center(
        child: Icon(
          Icons.image_outlined,
          color: Colors.grey,
          size: 32,
        ),
      ),
    );
  }

  Widget _buildDefaultError() {
    return Container(
      color: Colors.grey[100],
      child: const Center(
        child: Icon(
          Icons.broken_image_outlined,
          color: Colors.grey,
          size: 32,
        ),
      ),
    );
  }
}

/// 内存优化的列表项
class OptimizedListItem extends StatefulWidget {
  final Widget Function() builder;
  final double estimatedHeight;
  final bool keepAlive;

  const OptimizedListItem({
    super.key,
    required this.builder,
    this.estimatedHeight = 100,
    this.keepAlive = false,
  });

  @override
  State<OptimizedListItem> createState() => _OptimizedListItemState();
}

class _OptimizedListItemState extends State<OptimizedListItem>
    with AutomaticKeepAliveClientMixin {
  Widget? _cachedWidget;
  bool _isBuilt = false;

  @override
  bool get wantKeepAlive => widget.keepAlive && _isBuilt;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    if (!_isBuilt) {
      _cachedWidget = widget.builder();
      _isBuilt = true;
    }
    
    return _cachedWidget!;
  }
}
