import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:latlong2/latlong.dart';
import 'dart:math' as math;
import 'dart:async';
import '../utils/tianditu_utils.dart';
import '../main.dart' show setGlobalSearchBarFocusState;

/// 优化后的搜索组件
/// 默认周边搜索200公里，按距离排序显示结果
class OptimizedSearchBar extends StatefulWidget {
  final Function(LatLng) onLocationSelected;
  final VoidCallback? onSearchStarted;
  final VoidCallback? onSearchEnded;
  final LatLng? currentLocation;
  final String? hintText;

  const OptimizedSearchBar({
    super.key,
    required this.onLocationSelected,
    this.onSearchStarted,
    this.onSearchEnded,
    this.currentLocation,
    this.hintText = '搜索地点、地址',
  });

  @override
  State<OptimizedSearchBar> createState() => _OptimizedSearchBarState();
}

class _OptimizedSearchBarState extends State<OptimizedSearchBar> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  List<_SearchResultWithDistance> _searchResults = [];
  bool _isSearching = false;
  String? _errorMessage;

  // 搜索范围常量
  static const int _nearbyRadius = 200000; // 200公里，单位：米

  // 搜索取消和防抖
  Timer? _debounceTimer;
  String? _currentSearchQuery;
  
  @override
  void initState() {
    super.initState();
    _setupFocusListener();
  }

  void _setupFocusListener() {
    _focusNode.addListener(() {
      // 通知主应用搜索栏焦点状态，避免剪贴板检测干扰
      setGlobalSearchBarFocusState(_focusNode.hasFocus);

      if (_focusNode.hasFocus) {
        widget.onSearchStarted?.call();
      } else {
        widget.onSearchEnded?.call();
      }
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  /// 执行周边搜索
  Future<void> _performNearbySearch(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _searchResults = [];
        _errorMessage = null;
      });
      return;
    }

    // 检查是否是当前查询，避免重复搜索
    if (_currentSearchQuery == query && _isSearching) {
      return;
    }

    _currentSearchQuery = query;
    setState(() {
      _isSearching = true;
      _errorMessage = null;
    });

    try {
      List<SearchResult> results = [];

      if (widget.currentLocation != null) {
        // 使用普通搜索配合地图边界实现200公里范围搜索
        final mapBound = _calculateMapBound(widget.currentLocation!, _nearbyRadius / 1000); // 转换为公里
        results = await TianDiTuUtils.searchByName(
          query,
          queryType: 1, // 普通搜索
          mapBound: mapBound,
          count: 100, // 天地图API实际最大支持100条
        ) ?? [];

        // 处理普通搜索的不同结果类型
        if (results.isNotEmpty) {
          // 检查是否返回了统计结果（resultType=2）
          final hasStatisticsResults = results.any((result) => result.category == '统计结果');
          if (hasStatisticsResults) {
            debugPrint('检测到统计结果，显示城市选择列表');
            // 统计结果已经在TianDiTuUtils中被转换为SearchResult列表
            // 这些结果的category为'统计结果'，用户点击后会触发城市内搜索
          }
        }

        // 过滤200公里范围内的结果
        if (results.isNotEmpty) {
          results = results.where((result) {
            final distance = _calculateDistance(
              widget.currentLocation!.latitude,
              widget.currentLocation!.longitude,
              result.latitude,
              result.longitude,
            );
            return distance <= (_nearbyRadius / 1000); // 转换为公里进行比较
          }).toList();
        }

        debugPrint('200公里范围内搜索结果: ${results.length} 条');
      } else {
        // 没有位置信息时使用普通搜索
        results = await TianDiTuUtils.searchByName(
          query,
          queryType: 1, // 普通搜索
          count: 100, // 天地图API实际最大支持100条
        ) ?? [];

        debugPrint('普通搜索结果: ${results.length} 条');
      }

      // 检查查询是否仍然有效（用户可能已经输入了新的查询）
      if (mounted && _currentSearchQuery == query) {
        if (results.isNotEmpty) {
          // 计算距离并排序
          final resultsWithDistance = _calculateDistances(results);
          setState(() {
            _searchResults = resultsWithDistance;
            _isSearching = false;
            _errorMessage = null;
          });
        } else {
          // 没有搜索结果
          setState(() {
            _searchResults = [];
            _isSearching = false;
            _errorMessage = null;
          });
        }
      }
    } catch (e) {
      if (mounted && _currentSearchQuery == query) {
        setState(() {
          _searchResults = [];
          _isSearching = false;
          _errorMessage = '搜索失败，请检查网络连接后重试';
        });
      }
      debugPrint('搜索失败: $e');
    }
  }

  /// 计算地图边界（基于中心点和半径）
  String _calculateMapBound(LatLng center, double radiusKm) {
    // 计算纬度偏移（1度纬度约等于111公里）
    final double latOffset = radiusKm / 111.0;
    
    // 计算经度偏移（考虑纬度影响）
    final double lonOffset = radiusKm / (111.0 * math.cos(center.latitude * math.pi / 180));
    
    final double minLon = center.longitude - lonOffset;
    final double maxLon = center.longitude + lonOffset;
    final double minLat = center.latitude - latOffset;
    final double maxLat = center.latitude + latOffset;
    
    // 确保边界值在有效范围内
    final double boundedMinLon = math.max(-180, minLon);
    final double boundedMaxLon = math.min(180, maxLon);
    final double boundedMinLat = math.max(-90, minLat);
    final double boundedMaxLat = math.min(90, maxLat);
    
    final mapBound = '$boundedMinLon,$boundedMinLat,$boundedMaxLon,$boundedMaxLat';
    debugPrint('计算的地图边界: $mapBound (半径: ${radiusKm}km)');
    
    return mapBound;
  }

  /// 计算距离并排序
  List<_SearchResultWithDistance> _calculateDistances(List<SearchResult> results) {
    if (widget.currentLocation == null) {
      return results.map((result) => _SearchResultWithDistance(result, null)).toList();
    }

    final resultsWithDistance = results.map((result) {
      final distance = _calculateDistance(
        widget.currentLocation!.latitude,
        widget.currentLocation!.longitude,
        result.latitude,
        result.longitude,
      );
      return _SearchResultWithDistance(result, distance);
    }).toList();

    // 按距离排序
    resultsWithDistance.sort((a, b) {
      if (a.distance == null && b.distance == null) return 0;
      if (a.distance == null) return 1;
      if (b.distance == null) return -1;
      return a.distance!.compareTo(b.distance!);
    });

    return resultsWithDistance;
  }

  /// 计算距离（公里）
  double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // 地球半径，单位：公里
    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);
    final double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_degreesToRadians(lat1)) * math.cos(_degreesToRadians(lat2)) *
        math.sin(dLon / 2) * math.sin(dLon / 2);
    final double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) => degrees * (math.pi / 180);

  /// 格式化距离显示
  String _formatDistance(double? distance) {
    if (distance == null) return '';
    if (distance < 1) {
      return '${(distance * 1000).round()}m';
    } else {
      return '${distance.toStringAsFixed(1)}km';
    }
  }

  /// 格式化统计结果副标题
  String _formatStatisticsSubtitle(String address) {
    // address格式: "绍兴市 (289 个相关地点)"
    final regex = RegExp(r'\((\d+) 个相关地点\)');
    final match = regex.firstMatch(address);
    if (match != null) {
      final count = match.group(1);
      return '该市内有$count个结果，点击查看';
    }
    return '点击查看该城市内的搜索结果';
  }


  /// 清除搜索内容
  void _clearSearch() {
    _debounceTimer?.cancel();
    _currentSearchQuery = null;
    _controller.clear();
    setState(() {
      _searchResults = [];
      _errorMessage = null;
      _isSearching = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // [*调整参数*]搜索栏
        Container(
          margin: EdgeInsets.only(
            top: MediaQuery.of(context).padding.top - 20, // 往上移10像素
            left: 16,
            right: 16,
          ),
          child: Material(
            elevation: 4,
            borderRadius: BorderRadius.circular(10),
            shadowColor: Colors.black.withValues(alpha: 0.1),
            child: Container(
              height: 50,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              child: TextField(
                controller: _controller,
                focusNode: _focusNode,
                textAlign: TextAlign.left,
                textAlignVertical: TextAlignVertical.center,
                decoration: InputDecoration(
                  hintText: widget.hintText,
                  hintStyle: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 16,
                  ),
                  prefixIcon: Padding(
                    padding: const EdgeInsets.all(16),
                    child: _isSearching
                        ? SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.grey[400]!,
                              ),
                            ),
                          )
                        : FaIcon(
                            FontAwesomeIcons.magnifyingGlass,
                            size: 20,
                            color: Colors.grey[400],
                          ),
                  ),
                  suffixIcon: IconButton(
                    icon: const FaIcon(FontAwesomeIcons.xmark, size: 16),
                    onPressed: () {
                      // 清除搜索内容
                      _clearSearch();
                      // 失去焦点，关闭键盘
                      _focusNode.unfocus();
                      // 通知父组件返回地图页面（显示导航栏和右下角按钮）
                      widget.onSearchEnded?.call();
                    },
                    color: Colors.grey[400],
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 14,
                  ),
                ),
                style: const TextStyle(fontSize: 16),
                onChanged: (value) {
                  // 取消之前的防抖计时器
                  _debounceTimer?.cancel();

                  if (value.trim().isNotEmpty) {
                    // 统一防抖时间为400ms
                    _debounceTimer = Timer(const Duration(milliseconds: 400), () {
                      if (_controller.text == value && mounted) {
                        _performNearbySearch(value);
                      }
                    });
                  } else {
                    _clearSearch();
                  }
                },
                onSubmitted: (value) {
                  if (value.trim().isNotEmpty) {
                    _performSearchAndSelectFirst(value);
                  }
                },
              ),
            ),
          ),
        ),

        // 搜索结果、错误提示或空状态
        if (_errorMessage != null || _searchResults.isNotEmpty || (_controller.text.isNotEmpty && !_isSearching))
          Flexible(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.5, // 最大高度为屏幕高度的50%
              ),
              child: _buildSearchContent(),
            ),
          ),
      ],
    );
  }

  /// 构建搜索内容（结果、错误或空状态）
  Widget _buildSearchContent() {
    // 显示错误信息
    if (_errorMessage != null) {
      return Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              FontAwesomeIcons.triangleExclamation,
              size: 32,
              color: Colors.orange[600],
            ),
            const SizedBox(height: 12),
            Text(
              _errorMessage!,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            TextButton(
              onPressed: () {
                if (_controller.text.isNotEmpty) {
                  _performNearbySearch(_controller.text);
                }
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    // 显示搜索结果
    if (_searchResults.isNotEmpty) {
      return ListView.builder(
        padding: EdgeInsets.zero,
        itemCount: _searchResults.length,
        itemBuilder: (context, index) {
          final resultWithDistance = _searchResults[index];
          final result = resultWithDistance.result;
          final distance = resultWithDistance.distance;

          // 检查是否是统计结果（城市选择）
          final isStatisticsResult = result.category == '统计结果';

          return ListTile(
            dense: true,
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            minVerticalPadding: 0,
            leading: Icon(
              isStatisticsResult
                  ? FontAwesomeIcons.city // 城市图标
                  : FontAwesomeIcons.locationDot, // 地点图标
              size: 16,
              color: isStatisticsResult
                  ? Colors.blue[600] // 城市用蓝色
                  : Theme.of(context).primaryColor, // 地点用主题色
            ),
            title: Text(
              result.name,
              style: TextStyle(
                fontSize: 14,
                fontWeight: isStatisticsResult ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
            subtitle: Text(
              isStatisticsResult
                  ? _formatStatisticsSubtitle(result.address)
                  : result.address,
              style: TextStyle(
                fontSize: 12,
                color: isStatisticsResult
                    ? Colors.blue[600]
                    : Colors.grey[600],
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            trailing: isStatisticsResult
                ? Icon(
                    FontAwesomeIcons.chevronRight,
                    size: 12,
                    color: Colors.blue[600],
                  )
                : (distance != null
                    ? Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _formatDistance(distance),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      )
                    : null),
            onTap: () => _selectResult(result),
          );
        },
      );
    }

    // 显示空状态（用户输入了内容但没有结果）
    // 没找到结果时不显示任何内容
    return const SizedBox.shrink();
  }

  /// 选择搜索结果
  void _selectResult(SearchResult result) {
    debugPrint('选择搜索结果: ${result.name}, 类别: ${result.category}');

    // 检查是否是统计结果（城市选择）
    if (result.category == '统计结果') {
      // 用户点击了城市，在该城市内搜索原始关键词
      _performCitySearch(result, _controller.text);
      return;
    }

    // 普通地名结果，清空搜索框并移动地图
    _controller.clear();
    _focusNode.unfocus();

    // 清除所有搜索状态
    setState(() {
      _searchResults = [];
      _errorMessage = null;
      _isSearching = false;
    });

    // 检查坐标是否有效
    if (result.latitude != 0.0 || result.longitude != 0.0) {
      widget.onLocationSelected(LatLng(result.latitude, result.longitude));
    } else {
      debugPrint('⚠️ 坐标无效，无法移动地图');
    }
  }

  /// 在指定城市内搜索
  Future<void> _performCitySearch(SearchResult cityResult, String originalKeyword) async {
    final cityName = cityResult.name;
    final adminCode = cityResult.adminCode;

    debugPrint('在城市 $cityName (编码: $adminCode) 内搜索关键词: $originalKeyword');

    setState(() {
      _isSearching = true;
      _errorMessage = null;
    });

    try {
      // 使用普通搜索（queryType=1）配合specify参数在指定城市内搜索
      final results = await TianDiTuUtils.searchByName(
        originalKeyword,
        queryType: 1, // 普通搜索
        region: adminCode ?? cityName, // 优先使用行政区编码，否则使用城市名
        count: 100,
      ) ?? [];

      debugPrint('城市内搜索结果: ${results.length} 条');

      if (mounted) {
        if (results.isNotEmpty) {
          // 计算距离并排序
          final resultsWithDistance = _calculateDistances(results);
          setState(() {
            _searchResults = resultsWithDistance;
            _isSearching = false;
            _errorMessage = null;
          });
        } else {
          setState(() {
            _searchResults = [];
            _isSearching = false;
            _errorMessage = '在 $cityName 未找到包含 "$originalKeyword" 的地名';
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _searchResults = [];
          _isSearching = false;
          _errorMessage = '搜索失败，请检查网络连接后重试';
        });
      }
      debugPrint('城市内搜索失败: $e');
    }
  }

  /// 执行搜索并选择第一个结果
  Future<void> _performSearchAndSelectFirst(String query) async {
    await _performNearbySearch(query);

    // 如果有搜索结果，自动选择第一个
    if (_searchResults.isNotEmpty) {
      final firstResult = _searchResults.first.result;
      debugPrint('选择搜索结果: ${firstResult.name}, 坐标: (${firstResult.longitude}, ${firstResult.latitude})');

      _controller.text = firstResult.name;
      _focusNode.unfocus();

      // 检查坐标是否有效
      if (firstResult.latitude != 0.0 || firstResult.longitude != 0.0) {
        widget.onLocationSelected(LatLng(firstResult.latitude, firstResult.longitude));
      } else {
        debugPrint('⚠️ 坐标无效，无法移动地图');
      }
    } else {
      debugPrint('⚠️ 没有搜索结果');
    }
  }

}

/// 搜索结果与距离的组合类
class _SearchResultWithDistance {
  final SearchResult result;
  final double? distance; // 距离，单位：公里

  _SearchResultWithDistance(this.result, this.distance);
}