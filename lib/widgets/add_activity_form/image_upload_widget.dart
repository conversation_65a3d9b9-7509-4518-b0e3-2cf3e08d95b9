import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

/// 图片上传项目，包含文件和上传状态
class ImageUploadItem {
  final File file;
  final bool isFromCamera; // 是否来自相机拍摄
  double uploadProgress;
  bool isUploading;
  bool isCompleted;
  String? uploadedUrl;
  String? thumbnailUrl;
  String? errorMessage;

  ImageUploadItem({
    required this.file,
    this.isFromCamera = false,
    this.uploadProgress = 0.0,
    this.isUploading = false,
    this.isCompleted = false,
    this.uploadedUrl,
    this.thumbnailUrl,
    this.errorMessage,
  });
}

/// 图片上传组件
///
/// 功能：
/// - 图片选择（相机/相册）
/// - 图片预览
/// - 上传进度显示
/// - 图片删除
class ImageUploadWidget extends StatelessWidget {
  /// 已选择的图片列表
  final List<ImageUploadItem> selectedImages;
  
  /// 图片添加回调
  final Function(List<ImageUploadItem>) onImagesAdded;
  
  /// 图片删除回调
  final Function(int) onImageRemoved;
  
  /// 最大图片数量
  final int maxImages;
  
  /// 是否启用
  final bool enabled;

  const ImageUploadWidget({
    super.key,
    required this.selectedImages,
    required this.onImagesAdded,
    required this.onImageRemoved,
    this.maxImages = 9,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              '钓点照片',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const Spacer(),
            Text(
              '${selectedImages.length}/$maxImages',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 80,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: selectedImages.length + (selectedImages.length < maxImages ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == selectedImages.length) {
                // 添加照片的方框
                return Container(
                  width: 80,
                  height: 80,
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Colors.grey.shade400,
                      width: 2,
                      style: BorderStyle.solid,
                    ),
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.grey.shade100,
                  ),
                  child: InkWell(
                    onTap: enabled ? () => _showImagePickerOptions(context) : null,
                    borderRadius: BorderRadius.circular(8),
                    child: const Icon(
                      Icons.add,
                      size: 32,
                      color: Colors.grey,
                    ),
                  ),
                );
              } else {
                // 已选择的照片缩略图
                return Container(
                  width: 80,
                  height: 80,
                  margin: const EdgeInsets.only(right: 8),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: _buildImageThumbnail(selectedImages[index]),
                      ),
                      // 只在上传完成后显示删除按钮
                      if (selectedImages[index].isCompleted && enabled)
                        Positioned(
                          top: 4,
                          right: 4,
                          child: GestureDetector(
                            onTap: () => onImageRemoved(index),
                            child: Container(
                              width: 20,
                              height: 20,
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.close,
                                color: Colors.white,
                                size: 14,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                );
              }
            },
          ),
        ),
      ],
    );
  }

  /// 显示图片选择选项
  Future<void> _showImagePickerOptions(BuildContext context) async {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.camera_alt, color: Colors.blue),
                title: const Text('相机拍照'),
                subtitle: const Text('拍摄新照片（支持实拍认证）'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromCamera(context);
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library, color: Colors.green),
                title: const Text('从相册选择'),
                subtitle: const Text('选择已有照片'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromGallery(context);
                },
              ),
              ListTile(
                leading: const Icon(Icons.cancel, color: Colors.grey),
                title: const Text('取消'),
                onTap: () => Navigator.pop(context),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 从相机拍照
  Future<void> _pickImageFromCamera(BuildContext context) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        final newItem = ImageUploadItem(
          file: File(image.path),
          isFromCamera: true,
        );

        onImagesAdded([newItem]);

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('相机拍照成功，开始上传...'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('相机拍照失败: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('相机拍照失败，请重试'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  /// 从相册选择图片
  Future<void> _pickImageFromGallery(BuildContext context) async {
    try {
      final ImagePicker picker = ImagePicker();
      final List<XFile> images = await picker.pickMultiImage(
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (images.isNotEmpty) {
        final newItems = images
            .map((xFile) => ImageUploadItem(
                  file: File(xFile.path),
                  isFromCamera: false,
                ))
            .toList();

        onImagesAdded(newItems);

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('已选择 ${images.length} 张图片，开始上传...'),
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('选择图片失败: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('选择图片失败，请重试'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  /// 构建图片缩略图，支持黑白显示和渐进式彩色
  Widget _buildImageThumbnail(ImageUploadItem item) {
    Widget imageWidget = kIsWeb
        ? Image.network(
            item.file.path,
            width: 80,
            height: 80,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                width: 80,
                height: 80,
                color: Colors.grey[300],
                child: const Icon(Icons.image),
              );
            },
          )
        : Image.file(item.file, width: 80, height: 80, fit: BoxFit.cover);

    // 如果还没开始上传或正在上传，显示黑白图片
    if (!item.isCompleted) {
      imageWidget = ColorFiltered(
        colorFilter: const ColorFilter.matrix([
          0.2126, 0.7152, 0.0722, 0, 0, // Red channel
          0.2126, 0.7152, 0.0722, 0, 0, // Green channel
          0.2126, 0.7152, 0.0722, 0, 0, // Blue channel
          0, 0, 0, 1, 0, // Alpha channel
        ]),
        child: imageWidget,
      );

      // 如果正在上传，显示渐进式彩色效果
      if (item.isUploading && item.uploadProgress > 0) {
        imageWidget = Stack(
          children: [
            imageWidget, // 黑白背景
            ClipRect(
              child: Align(
                alignment: Alignment.centerLeft,
                widthFactor: item.uploadProgress,
                child: kIsWeb
                    ? Image.network(
                        item.file.path,
                        width: 80,
                        height: 80,
                        fit: BoxFit.cover,
                      )
                    : Image.file(
                        item.file,
                        width: 80,
                        height: 80,
                        fit: BoxFit.cover,
                      ),
              ),
            ),
          ],
        );
      }
    }

    return imageWidget;
  }
}
