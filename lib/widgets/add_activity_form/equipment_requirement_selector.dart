import 'package:flutter/material.dart';
import '../../theme/unified_theme.dart';

/// 装备要求选择器组件
///
/// 功能：
/// - 装备要求选择
/// - 可视化选择界面
/// - 状态管理
class EquipmentRequirementSelector extends StatelessWidget {
  /// 当前选中的装备要求
  final String selectedEquipment;

  /// 装备要求变更回调
  final Function(String) onChanged;

  /// 是否启用
  final bool enabled;

  /// 装备要求选项
  static const List<Map<String, String>> equipments = [
    {'value': '自带装备', 'label': '自带装备', 'emoji': '🎣'},
    {'value': '提供装备', 'label': '提供装备', 'emoji': '🎁'},
    {'value': 'AA制购买', 'label': 'AA制购买', 'emoji': '💰'},
  ];

  const EquipmentRequirementSelector({
    super.key,
    required this.selectedEquipment,
    required this.onChanged,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('装备要求', style: AppTheme.headlineSmall),
        const SizedBox(height: AppTheme.spacingS),
        Align(
          alignment: Alignment.centerLeft,
          child: Wrap(
            alignment: WrapAlignment.start,
            spacing: AppTheme.spacingXS,
            runSpacing: AppTheme.spacingXS,
            children:
                equipments.map((equipment) {
                  final isSelected = selectedEquipment == equipment['value'];

                  return GestureDetector(
                    onTap:
                        enabled ? () => onChanged(equipment['value']!) : null,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacingS,
                        vertical: AppTheme.spacingXS,
                      ),
                      decoration: AppTheme.selectorDecoration(
                        isSelected: isSelected,
                        selectedColor: Colors.purple,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            equipment['emoji']!,
                            style: const TextStyle(fontSize: 14),
                          ),
                          const SizedBox(width: AppTheme.spacingXS),
                          Text(
                            equipment['label']!,
                            style: AppTheme.labelMedium.copyWith(
                              fontSize: 13,
                              color:
                                  isSelected
                                      ? Colors.purple.shade700
                                      : AppTheme.textPrimaryColor,
                              fontWeight:
                                  isSelected
                                      ? FontWeight.w600
                                      : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
          ),
        ),
      ],
    );
  }
}
