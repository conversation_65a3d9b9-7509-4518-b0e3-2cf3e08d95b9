import 'package:flutter/material.dart';
import '../emoji_text_field.dart';
import '../../utils/tianditu_utils.dart';
import '../../theme/unified_theme.dart';
import 'package:latlong2/latlong.dart';

/// 活动标题输入组件
///
/// 功能：
/// - 活动标题输入
/// - 地名反查功能
/// - 表单验证
class ActivityTitleInput extends StatefulWidget {
  /// 标题控制器
  final TextEditingController controller;

  /// 当前位置
  final LatLng location;

  /// 验证函数
  final String? Function(String?)? validator;

  /// 是否启用
  final bool enabled;

  const ActivityTitleInput({
    super.key,
    required this.controller,
    required this.location,
    this.validator,
    this.enabled = true,
  });

  @override
  State<ActivityTitleInput> createState() => _ActivityTitleInputState();
}

class _ActivityTitleInputState extends State<ActivityTitleInput> {
  bool _isRefreshingLocationName = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('活动标题', style: AppTheme.headlineSmall),
        const SizedBox(height: AppTheme.spacingS),
        Row(
          children: [
            Expanded(
              child: EmojiTextField(
                controller: widget.controller,
                labelText: '活动标题 *',
                enabled: widget.enabled,
                validator: widget.validator,
              ),
            ),
            const SizedBox(width: AppTheme.spacingM),
            // 反查地名按钮
            Container(
              decoration: BoxDecoration(
                color:
                    _isRefreshingLocationName
                        ? AppTheme.warningColor.withValues(alpha: 0.1)
                        : AppTheme.successColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusS),
                border: Border.all(
                  color:
                      _isRefreshingLocationName
                          ? AppTheme.warningColor
                          : AppTheme.successColor,
                ),
              ),
              child: IconButton(
                onPressed:
                    _isRefreshingLocationName || !widget.enabled
                        ? null
                        : _refreshLocationName,
                icon:
                    _isRefreshingLocationName
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              AppTheme.warningColor,
                            ),
                          ),
                        )
                        : const Icon(
                          Icons.refresh_rounded,
                          color: AppTheme.successColor,
                          size: 20,
                        ),
                padding: const EdgeInsets.all(8),
                constraints: const BoxConstraints(minWidth: 40, minHeight: 40),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 反查地名功能
  Future<void> _refreshLocationName() async {
    setState(() {
      _isRefreshingLocationName = true;
    });

    try {
      debugPrint(
        '开始反查地名: ${widget.location.latitude}, ${widget.location.longitude}',
      );

      final locationName = await TianDiTuUtils.getBestLocationName(
        widget.location.longitude,
        widget.location.latitude,
      );

      if (locationName != null && locationName.trim().isNotEmpty) {
        setState(() {
          widget.controller.text = '${locationName.trim()}钓鱼活动';
        });
        debugPrint('反查地名成功: ${widget.controller.text}');

        // 显示成功提示
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white),
                  SizedBox(width: 8),
                  Text('地名获取成功'),
                ],
              ),
              duration: Duration(seconds: 2),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        debugPrint('反查地名失败：未获取到有效地名');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Row(
                children: [
                  Icon(Icons.warning, color: Colors.white),
                  SizedBox(width: 8),
                  Text('未获取到地名信息'),
                ],
              ),
              duration: Duration(seconds: 2),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('反查地名异常: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('获取地名失败: $e')),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshingLocationName = false;
        });
      }
    }
  }
}
