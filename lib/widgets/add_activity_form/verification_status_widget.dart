import 'package:flutter/material.dart';
import '../verification_tags.dart';
import '../verification_rules_dialog.dart';

/// 验证状态显示组件
///
/// 功能：
/// - 显示实地验证状态
/// - 显示实拍验证状态
/// - 提供验证规则说明
class VerificationStatusWidget extends StatelessWidget {
  /// 是否在实地
  final bool isOnSite;
  
  /// 是否为实拍照片
  final bool isCameraShot;
  
  /// 发布位置信息
  final Map<String, dynamic>? publishLocation;
  
  /// 是否有照片
  final bool hasImages;

  const VerificationStatusWidget({
    super.key,
    required this.isOnSite,
    required this.isCameraShot,
    this.publishLocation,
    required this.hasImages,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '发布认证',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        VerificationTags(
          isOnSite: publishLocation != null && isOnSite,
          isCameraShot: hasImages && isCameraShot,
          onOnSiteTap: () => showOnSiteRulesDialog(context),
          onCameraShotTap: () => showCameraRulesDialog(context),
          size: 20,
        ),
        const SizedBox(height: 8),
        _buildLocationStatus(),
        const SizedBox(height: 4),
        if (hasImages) _buildCameraStatus(),
      ],
    );
  }

  /// 构建位置状态文本
  Widget _buildLocationStatus() {
    if (publishLocation != null) {
      return Text(
        isOnSite
            ? '✅ 实地发布：您在钓点50米范围内发布'
            : '❌ 非实地发布：您不在钓点50米范围内',
        style: TextStyle(
          fontSize: 12,
          color: isOnSite ? Colors.green : Colors.orange,
        ),
      );
    } else {
      return const Text(
        '⚠️ 无法获取GPS信号，无法进行实地验证',
        style: TextStyle(
          fontSize: 12,
          color: Colors.grey,
        ),
      );
    }
  }

  /// 构建相机状态文本
  Widget _buildCameraStatus() {
    return Text(
      isCameraShot
          ? '✅ 实拍照片：所有照片均为相机拍摄'
          : '❌ 非实拍照片：包含从相册选择的照片',
      style: TextStyle(
        fontSize: 12,
        color: isCameraShot ? Colors.blue : Colors.orange,
      ),
    );
  }
}
