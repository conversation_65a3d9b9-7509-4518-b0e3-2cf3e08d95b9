import 'package:flutter/material.dart';
import '../../theme/unified_theme.dart';

/// 活动类型选择器组件
///
/// 功能：
/// - 活动类型选择
/// - 可视化选择界面
/// - 状态管理
class ActivityTypeSelector extends StatelessWidget {
  /// 当前选中的活动类型
  final String selectedActivityType;

  /// 活动类型变更回调
  final Function(String) onChanged;

  /// 是否启用
  final bool enabled;

  /// 活动类型选项
  static const List<Map<String, String>> activityTypes = [
    {'value': 'leisure', 'label': '休闲钓', 'emoji': '🎣'},
    {'value': 'competition', 'label': '竞技钓', 'emoji': '🏆'},
    {'value': 'night', 'label': '夜钓', 'emoji': '🌙'},
    {'value': 'wild', 'label': '野钓', 'emoji': '🏞️'},
  ];

  const ActivityTypeSelector({
    super.key,
    required this.selectedActivityType,
    required this.onChanged,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('活动类型', style: AppTheme.headlineSmall),
        const SizedBox(height: AppTheme.spacingS),
        Align(
          alignment: Alignment.centerLeft,
          child: Wrap(
            alignment: WrapAlignment.start,
            spacing: AppTheme.spacingXS,
            runSpacing: AppTheme.spacingXS,
            children:
                activityTypes.map((activityType) {
                  final isSelected =
                      selectedActivityType == activityType['value'];

                  return GestureDetector(
                    onTap:
                        enabled
                            ? () => onChanged(activityType['value']!)
                            : null,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacingS,
                        vertical: AppTheme.spacingXS,
                      ),
                      decoration: AppTheme.selectorDecoration(
                        isSelected: isSelected,
                        selectedColor: Colors.blue,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            activityType['emoji']!,
                            style: const TextStyle(fontSize: 14),
                          ),
                          const SizedBox(width: AppTheme.spacingXS),
                          Text(
                            activityType['label']!,
                            style: AppTheme.labelMedium.copyWith(
                              fontSize: 13,
                              color:
                                  isSelected
                                      ? Colors.blue.shade700
                                      : AppTheme.textPrimaryColor,
                              fontWeight:
                                  isSelected
                                      ? FontWeight.w600
                                      : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
          ),
        ),
      ],
    );
  }
}
