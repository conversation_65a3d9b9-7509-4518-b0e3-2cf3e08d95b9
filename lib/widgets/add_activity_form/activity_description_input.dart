import 'package:flutter/material.dart';
import '../emoji_text_field.dart';
import '../../theme/unified_theme.dart';

/// 活动描述输入组件
///
/// 功能：
/// - 多行文本输入
/// - 字数限制
/// - 表单验证
class ActivityDescriptionInput extends StatelessWidget {
  /// 描述控制器
  final TextEditingController controller;

  /// 最大字数
  final int maxLength;

  /// 最大行数
  final int maxLines;

  /// 是否启用
  final bool enabled;

  /// 验证函数
  final String? Function(String?)? validator;

  const ActivityDescriptionInput({
    super.key,
    required this.controller,
    this.maxLength = 500,
    this.maxLines = 3,
    this.enabled = true,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('活动描述', style: AppTheme.headlineSmall),
        const SizedBox(height: AppTheme.spacingS),
        EmojiTextField(
          controller: controller,
          labelText: '活动描述 *',
          hintText: '描述一下这次钓鱼活动的详情、安排等...',
          maxLines: maxLines,
          maxLength: maxLength,
          enabled: enabled,
          validator: validator,
        ),
      ],
    );
  }
}
