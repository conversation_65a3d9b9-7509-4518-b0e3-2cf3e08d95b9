import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';

import '../../theme/unified_theme.dart';
import '../../services/service_locator.dart';

import '../../models/spot_visibility.dart';
import '../../models/fishing_activity.dart';
import '../../utils/tianditu_utils.dart';

import 'activity_title_input.dart';
import 'activity_description_input.dart';
import 'activity_difficulty_selector.dart';
import 'equipment_requirement_selector.dart';
import 'activity_type_selector.dart';
import 'image_upload_widget.dart';
import 'image_upload_manager.dart';

import '../spot_visibility_selector.dart';

/// 重构后的添加活动表单组件
///
/// 功能：
/// - 模块化的表单组件
/// - 实时位置验证
/// - 即时图片上传
/// - 现代化UI设计
class AddActivityForm extends StatefulWidget {
  /// 活动位置
  final LatLng location;

  /// 建议的活动标题
  final String? suggestedTitle;

  /// 提交成功回调
  final Function(FishingActivity activity) onSubmitSuccess;

  /// 取消回调
  final VoidCallback? onCancel;

  const AddActivityForm({
    super.key,
    required this.location,
    this.suggestedTitle,
    required this.onSubmitSuccess,
    this.onCancel,
  });

  @override
  State<AddActivityForm> createState() => _AddActivityFormState();
}

class _AddActivityFormState extends State<AddActivityForm> {
  final _formKey = GlobalKey<FormState>();
  final _imageUploadManager = ImageUploadManager();

  // 表单控制器
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationNameController = TextEditingController();

  // 表单状态
  String _selectedDifficulty = '新手友好';
  String _selectedEquipment = '自带装备';
  String _selectedActivityType = 'leisure'; // 默认休闲钓
  SpotVisibility _selectedVisibility = SpotVisibility.public;
  final List<ImageUploadItem> _selectedImages = [];

  // 活动时间
  DateTime? _startTime;
  DateTime? _endTime;

  // UI状态
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();

    // 如果有建议标题，设置到输入框
    if (widget.suggestedTitle != null && widget.suggestedTitle!.isNotEmpty) {
      _titleController.text = widget.suggestedTitle!;
    }

    // 自动获取地名
    _refreshLocationName();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _locationNameController.dispose();
    super.dispose();
  }

  /// 刷新位置名称
  Future<void> _refreshLocationName() async {
    try {
      final result = await TianDiTuUtils.reverseGeocode(
        widget.location.longitude,
        widget.location.latitude,
      );

      if (result != null && mounted) {
        final locationName = result['formatted_address'] ?? result['address'];
        if (locationName != null) {
          _locationNameController.text = locationName;

          // 如果标题为空，自动填充标题
          if (_titleController.text.isEmpty) {
            _titleController.text = '$locationName 钓鱼活动';
          }
        }
      }
    } catch (e) {
      debugPrint('❌ [活动创建] 获取位置名称失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('发起一起钓鱼'),
        backgroundColor: AppTheme.surfaceColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: widget.onCancel,
        ),
        actions: [
          TextButton(
            onPressed: _canSubmit() ? _submitForm : null,
            child: Text(
              _imageUploadManager.hasUploadingImages(_selectedImages)
                  ? _imageUploadManager.getUploadStatusText(_selectedImages)
                  : '发布',
              style: AppTheme.labelLarge.copyWith(
                color:
                    _canSubmit()
                        ? AppTheme.designPrimaryColor
                        : AppTheme.textHintColor,
              ),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppTheme.spacingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 活动标题
              _buildFormCard(
                child: ActivityTitleInput(
                  controller: _titleController,
                  location: widget.location,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '请输入活动标题';
                    }
                    return null;
                  },
                ),
              ),

              const SizedBox(height: AppTheme.spacingM),

              // 图片上传
              _buildFormCard(
                child: ImageUploadWidget(
                  selectedImages: _selectedImages,
                  onImagesAdded: _handleImagesAdded,
                  onImageRemoved: _handleImageRemoved,
                ),
              ),

              const SizedBox(height: AppTheme.spacingM),

              // 活动描述
              _buildFormCard(
                child: ActivityDescriptionInput(
                  controller: _descriptionController,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '请输入活动描述';
                    }
                    return null;
                  },
                ),
              ),

              const SizedBox(height: AppTheme.spacingM),

              // 活动地点
              _buildFormCard(child: _buildLocationInput()),

              const SizedBox(height: AppTheme.spacingM),

              // 活动时间
              _buildFormCard(child: _buildTimeInput()),

              const SizedBox(height: AppTheme.spacingM),

              // 活动难度、装备要求和活动类型选择
              _buildFormCard(
                child: Column(
                  children: [
                    ActivityDifficultySelector(
                      selectedDifficulty: _selectedDifficulty,
                      onChanged:
                          (value) =>
                              setState(() => _selectedDifficulty = value),
                    ),
                    const SizedBox(height: AppTheme.spacingL),
                    EquipmentRequirementSelector(
                      selectedEquipment: _selectedEquipment,
                      onChanged:
                          (value) => setState(() => _selectedEquipment = value),
                    ),
                    const SizedBox(height: AppTheme.spacingL),
                    ActivityTypeSelector(
                      selectedActivityType: _selectedActivityType,
                      onChanged:
                          (value) =>
                              setState(() => _selectedActivityType = value),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppTheme.spacingM),

              // 可见性设置
              _buildFormCard(
                child: SpotVisibilitySelector(
                  initialVisibility: _selectedVisibility,
                  onChanged:
                      (value, conditions) =>
                          setState(() => _selectedVisibility = value),
                ),
              ),

              const SizedBox(height: AppTheme.spacingXL),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建表单卡片
  Widget _buildFormCard({required Widget child}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: AppTheme.cardDecoration,
      child: child,
    );
  }

  /// 处理图片添加
  void _handleImagesAdded(List<ImageUploadItem> newImages) {
    setState(() {
      _selectedImages.addAll(newImages);
    });

    // 立即开始上传
    _imageUploadManager.uploadSelectedImages(newImages, (updatedItem) {
      if (mounted) {
        setState(() {
          // 触发UI更新
        });
      }
    });
  }

  /// 处理图片删除
  void _handleImageRemoved(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  /// 构建位置输入组件
  Widget _buildLocationInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('活动地点', style: AppTheme.headlineSmall),
        const SizedBox(height: AppTheme.spacingS),
        TextFormField(
          controller: _locationNameController,
          decoration: const InputDecoration(
            labelText: '活动地点 *',
            hintText: '输入具体地点名称',
            border: OutlineInputBorder(),
            suffixIcon: Icon(Icons.location_on),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入活动地点';
            }
            return null;
          },
        ),
        const SizedBox(height: AppTheme.spacingS),
        Text(
          '坐标: ${widget.location.latitude.toStringAsFixed(6)}, ${widget.location.longitude.toStringAsFixed(6)}',
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
      ],
    );
  }

  /// 构建时间输入组件
  Widget _buildTimeInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('活动时间', style: AppTheme.headlineSmall),
        const SizedBox(height: AppTheme.spacingS),
        // 开始时间
        ListTile(
          contentPadding: EdgeInsets.zero,
          leading: const Icon(Icons.access_time),
          title: const Text('开始时间'),
          subtitle: Text(
            _startTime != null
                ? '${_startTime!.year}-${_startTime!.month.toString().padLeft(2, '0')}-${_startTime!.day.toString().padLeft(2, '0')} ${_startTime!.hour.toString().padLeft(2, '0')}:${_startTime!.minute.toString().padLeft(2, '0')}'
                : '请选择开始时间',
          ),
          onTap: () => _selectDateTime(true),
        ),
        const Divider(),
        // 结束时间
        ListTile(
          contentPadding: EdgeInsets.zero,
          leading: const Icon(Icons.access_time_filled),
          title: const Text('结束时间'),
          subtitle: Text(
            _endTime != null
                ? '${_endTime!.year}-${_endTime!.month.toString().padLeft(2, '0')}-${_endTime!.day.toString().padLeft(2, '0')} ${_endTime!.hour.toString().padLeft(2, '0')}:${_endTime!.minute.toString().padLeft(2, '0')}'
                : '请选择结束时间',
          ),
          onTap: () => _selectDateTime(false),
        ),
      ],
    );
  }

  /// 选择日期时间
  Future<void> _selectDateTime(bool isStartTime) async {
    final now = DateTime.now();
    final initialDate =
        isStartTime
            ? (_startTime ?? now.add(const Duration(hours: 1)))
            : (_endTime ??
                _startTime?.add(const Duration(hours: 2)) ??
                now.add(const Duration(hours: 3)));

    final date = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: now,
      lastDate: now.add(const Duration(days: 365)),
    );

    if (date != null && mounted) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(initialDate),
      );

      if (time != null && mounted) {
        final selectedDateTime = DateTime(
          date.year,
          date.month,
          date.day,
          time.hour,
          time.minute,
        );

        setState(() {
          if (isStartTime) {
            _startTime = selectedDateTime;
            // 如果结束时间早于开始时间，自动调整结束时间
            if (_endTime != null && _endTime!.isBefore(selectedDateTime)) {
              _endTime = selectedDateTime.add(const Duration(hours: 2));
            }
          } else {
            _endTime = selectedDateTime;
          }
        });
      }
    }
  }

  /// 检查是否可以提交
  bool _canSubmit() {
    return !_isSubmitting &&
        !_imageUploadManager.hasUploadingImages(_selectedImages) &&
        _titleController.text.trim().isNotEmpty &&
        _startTime != null &&
        _endTime != null;
  }

  /// 提交表单
  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;
    if (_isSubmitting) return;

    // 验证时间
    if (_startTime == null || _endTime == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请选择活动时间')));
      return;
    }

    if (_endTime!.isBefore(_startTime!)) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('结束时间不能早于开始时间')));
      return;
    }

    setState(() => _isSubmitting = true);

    try {
      // 创建活动记录
      final activity = await Services.activity.createActivity(
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        location: widget.location,
        locationName: _locationNameController.text.trim(),
        startTime: _startTime!,
        endTime: _endTime!,
      );

      if (activity != null) {
        // 成功回调
        widget.onSubmitSuccess(activity);
      } else {
        throw Exception('创建活动失败');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('发布失败: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }
}
