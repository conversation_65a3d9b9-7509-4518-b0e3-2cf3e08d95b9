import 'package:flutter/material.dart';
import '../../theme/unified_theme.dart';

/// 活动难度选择器组件
///
/// 功能：
/// - 活动难度选择
/// - 可视化选择界面
/// - 状态管理
class ActivityDifficultySelector extends StatelessWidget {
  /// 当前选中的难度
  final String selectedDifficulty;

  /// 难度变更回调
  final Function(String) onChanged;

  /// 是否启用
  final bool enabled;

  /// 难度选项
  static const List<Map<String, String>> difficulties = [
    {'value': '新手友好', 'label': '新手友好', 'emoji': '🌱'},
    {'value': '中等难度', 'label': '中等难度', 'emoji': '⚖️'},
    {'value': '高级挑战', 'label': '高级挑战', 'emoji': '🏆'},
  ];

  const ActivityDifficultySelector({
    super.key,
    required this.selectedDifficulty,
    required this.onChanged,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('活动难度', style: AppTheme.headlineSmall),
        const SizedBox(height: AppTheme.spacingS),
        Align(
          alignment: Alignment.centerLeft,
          child: Wrap(
            alignment: WrapAlignment.start,
            spacing: AppTheme.spacingXS,
            runSpacing: AppTheme.spacingXS,
            children:
                difficulties.map((level) {
                  final isSelected = selectedDifficulty == level['value'];

                  return GestureDetector(
                    onTap: enabled ? () => onChanged(level['value']!) : null,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacingS,
                        vertical: AppTheme.spacingXS,
                      ),
                      decoration: AppTheme.selectorDecoration(
                        isSelected: isSelected,
                        selectedColor: AppTheme.warningColor,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            level['emoji']!,
                            style: const TextStyle(fontSize: 14),
                          ),
                          const SizedBox(width: AppTheme.spacingXS),
                          Text(
                            level['label']!,
                            style: AppTheme.labelMedium.copyWith(
                              fontSize: 13,
                              color:
                                  isSelected
                                      ? AppTheme.warningColor
                                      : AppTheme.textPrimaryColor,
                              fontWeight:
                                  isSelected
                                      ? FontWeight.w600
                                      : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
          ),
        ),
      ],
    );
  }
}
