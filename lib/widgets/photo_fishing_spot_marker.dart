import 'package:flutter/material.dart';
import '../models/fishing_spot.dart';
import '../models/spot_photo.dart';
import '../services/unified_image_service.dart';

/// 带照片的钓点标记组件
///
/// 特性：
/// - 圆形照片显示在中心
/// - 底部有尖点指向钓点位置
/// - 支持点击交互
/// - 如果没有照片则回退到emoji显示
/// - 支持加载状态和错误处理
/// - 🔧 修复：改为StatefulWidget避免地图移动时重复刷新图片
class PhotoFishingSpotMarker extends StatefulWidget {
  final FishingSpot spot;
  final VoidCallback? onTap;
  final double size;
  final Future<List<SpotPhoto>> Function(String spotId)? getPhotos;
  final Future<int> Function(String spotId)? getLikesCount;

  const PhotoFishingSpotMarker({
    super.key,
    required this.spot,
    this.onTap,
    this.size = 60.0,
    this.getPhotos,
    this.getLikesCount,
  });

  @override
  State<PhotoFishingSpotMarker> createState() => _PhotoFishingSpotMarkerState();
}

class _PhotoFishingSpotMarkerState extends State<PhotoFishingSpotMarker> {
  List<SpotPhoto> _photos = [];
  int _likesCount = 0;
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// 只在初始化时加载一次数据，避免地图移动时重复加载
  Future<void> _loadData() async {
    try {
      final results = await Future.wait([
        widget.getPhotos?.call(widget.spot.id) ?? Future.value(<SpotPhoto>[]),
        widget.getLikesCount?.call(widget.spot.id) ?? Future.value(0),
      ]);

      if (mounted) {
        setState(() {
          _photos = results[0] as List<SpotPhoto>;
          _likesCount = results[1] as int;
          _isLoading = false;
          _hasError = false;
        });
      }
    } catch (e) {
      debugPrint('❌ [PhotoMarker] 加载数据失败: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      // 加载状态：显示简单的占位符
      return GestureDetector(
        onTap: widget.onTap,
        child: Container(
          width: widget.size * 1.5,
          height: widget.size * 1.5,
          alignment: Alignment.center,
          child: SizedBox(
            width: widget.size,
            height: widget.size,
            child: CustomPaint(
              painter: _PhotoMarkerPainter(
                likesCount: 0,
                size: widget.size,
              ),
              child: Center(
                child: SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Colors.grey[400],
                  ),
                ),
              ),
            ),
          ),
        ),
      );
    }

    if (_hasError) {
      // 错误状态：显示emoji标记作为后备
      return _buildEmojiMarker();
    }

    // 正常状态：显示照片或emoji标记
    return GestureDetector(
      onTap: widget.onTap,
      onLongPress: widget.onTap,
      behavior: HitTestBehavior.opaque,
      child: Container(
        width: widget.size * 1.5, // 扩大点击区域
        height: widget.size * 1.5,
        alignment: Alignment.center,
        child: SizedBox(
          width: widget.size,
          height: widget.size,
          child: CustomPaint(
            painter: _PhotoMarkerPainter(
              likesCount: _likesCount,
              size: widget.size,
            ),
            child: _buildMarkerContent(),
          ),
        ),
      ),
    );
  }

  /// 构建标记内容（照片或emoji）
  Widget _buildMarkerContent() {
    // 如果有照片，显示第一张照片的缩略图
    if (_photos.isNotEmpty) {
      return _buildPhotoContent();
    } else {
      // 没有照片时显示emoji
      return _buildEmojiContent();
    }
  }

  /// 构建照片内容
  Widget _buildPhotoContent() {
    final photo = _photos.first;
    final circleRadius = widget.size * 0.35;
    
    return Positioned(
      top: widget.size * 0.1, // 圆形在上方
      left: widget.size * 0.15,
      child: Container(
        width: circleRadius * 2,
        height: circleRadius * 2,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.white,
            width: 2.0,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipOval(
          child: _buildPhotoImage(photo),
        ),
      ),
    );
  }

  /// 构建照片图像（优化：添加稳定key避免重复刷新）
  Widget _buildPhotoImage(SpotPhoto photo) {
    final imageService = UnifiedImageService();
    
    return imageService.buildCachedSignedImage(
      key: ValueKey('photo_${photo.id}'), // 🔧 关键修复：添加稳定的key
      originalUrl: photo.thumbnailUrl ?? photo.url,
      fit: BoxFit.cover,
      placeholder: Container(
        color: Colors.grey[200],
        child: const Center(
          child: SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: Colors.grey,
            ),
          ),
        ),
      ),
      errorWidget: Container(
        color: Colors.grey[200],
        child: Icon(
          Icons.image_not_supported,
          color: Colors.grey[400],
          size: 16,
        ),
      ),
    );
  }

  /// 构建emoji内容（回退方案）
  Widget _buildEmojiContent() {
    final circleRadius = widget.size * 0.35;
    
    return Positioned(
      top: widget.size * 0.1,
      left: widget.size * 0.15,
      child: Container(
        width: circleRadius * 2,
        height: circleRadius * 2,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: _getBackgroundColor(_likesCount),
          border: Border.all(
            color: Colors.white,
            width: 2.0,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Text(
            widget.spot.displayFishEmoji,
            style: TextStyle(
              fontSize: circleRadius * 1.2,
              height: 1.0,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建emoji标记（错误时的后备方案）
  Widget _buildEmojiMarker() {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        width: widget.size * 1.5,
        height: widget.size * 1.5,
        alignment: Alignment.center,
        child: SizedBox(
          width: widget.size,
          height: widget.size,
          child: CustomPaint(
            painter: _PhotoMarkerPainter(
              likesCount: _likesCount,
              size: widget.size,
            ),
            child: _buildEmojiContent(),
          ),
        ),
      ),
    );
  }

  /// 根据点赞数量计算背景颜色（用于emoji模式）
  Color _getBackgroundColor(int likes) {
    if (likes <= 0) {
      return const Color(0xFFF5F5F5);
    }
    
    final normalizedLikes = (likes / 50.0).clamp(0.0, 1.0);
    
    if (normalizedLikes < 0.5) {
      return Color.lerp(
        const Color(0xFFE3F2FD), // 浅蓝
        const Color(0xFFFFF3E0), // 浅黄
        normalizedLikes * 2,
      )!;
    } else {
      return Color.lerp(
        const Color(0xFFFFF3E0), // 浅黄
        const Color(0xFFFFEBEE), // 浅红
        (normalizedLikes - 0.5) * 2,
      )!;
    }
  }
}

/// 照片标记绘制器
/// 只负责绘制背景形状和尖点
class _PhotoMarkerPainter extends CustomPainter {
  final int likesCount;
  final double size;

  _PhotoMarkerPainter({
    required this.likesCount,
    required this.size,
  });

  @override
  void paint(Canvas canvas, Size canvasSize) {
    // 绘制尖点指向地面
    _drawPointer(canvas, canvasSize);
  }

  /// 绘制指向地面的尖点
  void _drawPointer(Canvas canvas, Size canvasSize) {
    final circleRadius = size * 0.35;
    final circleCenter = Offset(
      canvasSize.width / 2,
      size * 0.25 + circleRadius, // 圆心位置
    );
    
    // 绘制尖点三角形
    final triangleHeight = size * 0.25;
    final triangleWidth = size * 0.12;
    final triangleTop = circleCenter.dy + circleRadius;
    final triangleBottom = triangleTop + triangleHeight;

    final trianglePath = Path();
    trianglePath.moveTo(circleCenter.dx - triangleWidth, triangleTop);
    trianglePath.lineTo(circleCenter.dx + triangleWidth, triangleTop);
    trianglePath.lineTo(circleCenter.dx, triangleBottom);
    trianglePath.close();

    final paint = Paint()
      ..style = PaintingStyle.fill
      ..color = Colors.white;

    canvas.drawPath(trianglePath, paint);

    // 绘制尖点边框
    final borderPaint = Paint()
      ..style = PaintingStyle.stroke
      ..color = Colors.white
      ..strokeWidth = 2.0;

    canvas.drawPath(trianglePath, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false; // 尖点形状不会改变
  }
}

/// 照片钓点标记构建器
class PhotoFishingSpotMarkerBuilder {
  /// 创建带照片的钓点标记
  ///
  /// [spot] 钓点数据
  /// [onTap] 点击回调
  /// [size] 标记大小
  /// [getPhotos] 获取照片列表的异步函数
  /// [getLikesCount] 获取点赞数量的异步函数
  static Widget buildMarker({
    required FishingSpot spot,
    VoidCallback? onTap,
    double size = 60.0,
    Future<List<SpotPhoto>> Function(String spotId)? getPhotos,
    Future<int> Function(String spotId)? getLikesCount,
  }) {
    // 🔧 关键修复：直接使用StatefulWidget，避免FutureBuilder重复执行
    return PhotoFishingSpotMarker(
      key: ValueKey('marker_${spot.id}'), // 添加稳定的key
      spot: spot,
      onTap: onTap,
      size: size,
      getPhotos: getPhotos,
      getLikesCount: getLikesCount,
    );
  }
}