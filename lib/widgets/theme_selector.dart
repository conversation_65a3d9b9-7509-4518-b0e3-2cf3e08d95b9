import 'package:flutter/material.dart';
import '../theme/app_theme_type.dart';
import '../theme/app_theme_manager.dart';
import '../theme/material3_theme_data.dart';

/// 主题选择器组件
/// 
/// 提供主题类型和主题模式的选择界面
class ThemeSelector extends StatefulWidget {
  const ThemeSelector({super.key});

  @override
  State<ThemeSelector> createState() => _ThemeSelectorState();
}

class _ThemeSelectorState extends State<ThemeSelector> {
  late AppThemeManager _themeManager;

  @override
  void initState() {
    super.initState();
    _themeManager = AppThemeManager.instance;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('主题设置'),
        elevation: 0,
      ),
      body: ListenableBuilder(
        listenable: _themeManager,
        builder: (context, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 主题类型选择
                _buildSectionTitle('选择主题风格'),
                const SizedBox(height: 12),
                _buildThemeTypeGrid(),
                
                const SizedBox(height: 32),
                
                // 主题模式选择
                _buildSectionTitle('显示模式'),
                const SizedBox(height: 12),
                _buildThemeModeList(),
                
                const SizedBox(height: 32),
                
                // 动态颜色设置
                _buildSectionTitle('高级设置'),
                const SizedBox(height: 12),
                _buildDynamicColorSetting(),
                
                const SizedBox(height: 32),
                
                // 预览区域
                _buildSectionTitle('预览效果'),
                const SizedBox(height: 12),
                _buildPreviewArea(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildThemeTypeGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.4, // 增加高度比例，给更多垂直空间
      ),
      itemCount: AppThemeType.values.length,
      itemBuilder: (context, index) {
        final themeType = AppThemeType.values[index];
        final isSelected = _themeManager.currentThemeType == themeType;
        
        return _ThemeTypeCard(
          themeType: themeType,
          isSelected: isSelected,
          onTap: () => _themeManager.setThemeType(themeType),
        );
      },
    );
  }

  Widget _buildThemeModeList() {
    return Column(
      children: ThemeMode.values.map((mode) {
        final isSelected = _themeManager.currentThemeMode == mode;
        
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: Icon(_themeManager.getThemeModeIcon(mode)),
            title: Text(_themeManager.getThemeModeDisplayName(mode)),
            subtitle: Text(_themeManager.getThemeModeDescription(mode)),
            trailing: isSelected 
                ? Icon(Icons.check_circle, color: Theme.of(context).colorScheme.primary)
                : const Icon(Icons.radio_button_unchecked),
            onTap: () => _themeManager.setThemeMode(mode),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildDynamicColorSetting() {
    return Card(
      child: SwitchListTile(
        title: const Text('使用系统动态颜色'),
        subtitle: const Text('在支持的设备上使用系统壁纸颜色'),
        value: _themeManager.useSystemDynamicColor,
        onChanged: (value) => _themeManager.setUseSystemDynamicColor(value),
        secondary: const Icon(Icons.palette),
      ),
    );
  }

  Widget _buildPreviewArea() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '预览',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            const SizedBox(height: 16),
            
            // 按钮预览
            Row(
              children: [
                Expanded(
                  child: FilledButton(
                    onPressed: () {},
                    child: const Text('主要按钮'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {},
                    child: const Text('次要按钮'),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 开关和复选框预览
            Row(
              children: [
                Switch(
                  value: true,
                  onChanged: (value) {},
                ),
                const SizedBox(width: 16),
                Checkbox(
                  value: true,
                  onChanged: (value) {},
                ),
                const SizedBox(width: 16),
                Radio<bool>(
                  value: true,
                  groupValue: true,
                  onChanged: (value) {},
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 颜色预览
            _buildColorPreview(),
          ],
        ),
      ),
    );
  }

  Widget _buildColorPreview() {
    final colorScheme = Theme.of(context).colorScheme;
    final colors = [
      ('主色调', colorScheme.primary),
      ('辅助色', colorScheme.secondary),
      ('第三色', colorScheme.tertiary),
      ('表面色', colorScheme.surface),
    ];

    return Row(
      children: colors.map((colorInfo) {
        return Expanded(
          child: Column(
            children: [
              Container(
                height: 40,
                decoration: BoxDecoration(
                  color: colorInfo.$2,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                colorInfo.$1,
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}

/// 主题类型卡片组件
class _ThemeTypeCard extends StatelessWidget {
  final AppThemeType themeType;
  final bool isSelected;
  final VoidCallback onTap;

  const _ThemeTypeCard({
    required this.themeType,
    required this.isSelected,
    required this.onTap,
  });

  /// 获取简短的主题描述
  String _getShortDescription(AppThemeType themeType) {
    switch (themeType) {
      case AppThemeType.oceanBlue:
        return '专业可靠的钓鱼氛围';
      case AppThemeType.natureGreen:
        return '贴近自然的生态感';
      case AppThemeType.sunsetWarm:
        return '温暖活力的时光';
      case AppThemeType.nightFishing:
        return '神秘的现代简约';
      case AppThemeType.classicBusiness:
        return '经典专业商务风';
    }
  }

  @override
  Widget build(BuildContext context) {
    final previewColors = Material3ThemeData.getPreviewColors(themeType);
    
    return Card(
      elevation: isSelected ? 4 : 1,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(8), // 减少内边距
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: isSelected 
                ? Border.all(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2,
                  )
                : null,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 主题图标和名称
              Row(
                children: [
                  Text(
                    themeType.iconEmoji,
                    style: const TextStyle(fontSize: 20), // 减小图标大小
                  ),
                  const SizedBox(width: 6), // 减小间距
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          themeType.displayName,
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            fontSize: 13, // 减小字体
                          ),
                        ),
                        Text(
                          themeType.englishName,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                            fontSize: 10, // 减小字体
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (isSelected)
                    Icon(
                      Icons.check_circle,
                      color: Theme.of(context).colorScheme.primary,
                      size: 16, // 减小图标大小
                    ),
                ],
              ),
              
              const SizedBox(height: 8), // 减小间距
              
              // 颜色预览
              Row(
                children: previewColors.take(3).map((color) {
                  return Expanded(
                    child: Container(
                      height: 20, // 减小高度
                      margin: const EdgeInsets.only(right: 3), // 减小间距
                      decoration: BoxDecoration(
                        color: color,
                        borderRadius: BorderRadius.circular(3), // 减小圆角
                      ),
                    ),
                  );
                }).toList(),
              ),
              
              const SizedBox(height: 6), // 减小间距
              
              // 主题描述 - 缩短描述文本
              Text(
                _getShortDescription(themeType),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  fontSize: 10, // 减小字体
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}