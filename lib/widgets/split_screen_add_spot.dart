import 'dart:async';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import 'package:uuid/uuid.dart';

import '../models/fishing_spot.dart';
import '../models/spot_visibility.dart';
import '../theme/unified_theme.dart';
import '../services/location_verification_service.dart';
import '../services/service_locator.dart';
import '../config/pocketbase_config.dart';
import 'package:path/path.dart' as path;

// 导入新的模块化组件
import 'add_spot_form/spot_name_input.dart';
import 'add_spot_form/spot_description_input.dart';
import 'add_spot_form/water_level_selector.dart';
import 'add_spot_form/bait_selector.dart';
import 'add_spot_form/fish_type_selector.dart';
import 'add_spot_form/image_upload_widget.dart';
import 'add_spot_form/image_upload_manager.dart';
import 'add_spot_form/verification_status_widget.dart';
import 'spot_visibility_selector.dart';
import 'location_status_widget.dart';

/// 分屏添加钓点组件
class SplitScreenAddSpot extends StatefulWidget {
  /// 钓点位置
  final LatLng location;

  /// 位置更新回调
  final Function(LatLng) onLocationChanged;

  /// 关闭回调
  final VoidCallback onClose;

  /// 成功添加钓点回调
  final Function(FishingSpot) onSpotAdded;

  /// 建议的钓点名称
  final String? suggestedName;

  const SplitScreenAddSpot({
    super.key,
    required this.location,
    required this.onLocationChanged,
    required this.onClose,
    required this.onSpotAdded,
    this.suggestedName,
  });

  @override
  State<SplitScreenAddSpot> createState() => _SplitScreenAddSpotState();
}

class _SplitScreenAddSpotState extends State<SplitScreenAddSpot> {
  final _formKey = GlobalKey<FormState>();
  final _imageUploadManager = ImageUploadManager();

  // 表单控制器
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  // 表单状态
  String _selectedWaterLevel = '正常';
  String _selectedBait = '商品饵';
  String _selectedFishType = 'carp'; // 默认鲤鱼
  SpotVisibility _selectedVisibility = SpotVisibility.public;
  Map<String, dynamic>? _visibilityConditions;
  final List<ImageUploadItem> _selectedImages = [];

  // 位置验证状态
  Position? _publishLocation;
  bool _isOnSite = false;
  bool _isCameraShot = false;

  // UI状态
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    // 如果有建议的钓点名称，设置到名称字段中
    if (widget.suggestedName != null &&
        widget.suggestedName!.trim().isNotEmpty) {
      _nameController.text = widget.suggestedName!;
    }
  }

  @override
  void didUpdateWidget(SplitScreenAddSpot oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 当建议名称更新时，如果输入框为空，则填充新的建议名称
    if (widget.suggestedName != oldWidget.suggestedName &&
        widget.suggestedName != null &&
        widget.suggestedName!.trim().isNotEmpty &&
        _nameController.text.trim().isEmpty) {
      _nameController.text = widget.suggestedName!;
    }

    // 当位置发生变化时，重新计算实地验证状态
    if (widget.location != oldWidget.location) {
      _updateOnSiteVerificationStatus();
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  /// 更新实地验证状态
  void _updateOnSiteVerificationStatus() {
    if (_publishLocation != null) {
      _isOnSite = LocationVerificationService.isOnSite(
        widget.location,
        _publishLocation!,
      );
      if (mounted) {
        setState(() {});
      }
    }
  }

  /// 处理图片添加
  void _handleImagesAdded(List<ImageUploadItem> newImages) {
    setState(() {
      _selectedImages.addAll(newImages);
      // 更新实拍状态
      _updateCameraShotStatus();
    });

    // 开始上传图片
    _imageUploadManager.uploadSelectedImages(newImages, (item) {
      setState(() {
        // 图片状态已更新
      });
    });
  }

  /// 处理图片删除
  void _handleImageRemoved(int index) {
    if (index >= 0 && index < _selectedImages.length) {
      setState(() {
        _selectedImages.removeAt(index);
        // 更新实拍状态
        _updateCameraShotStatus();
      });
    }
  }

  /// 更新实拍状态
  void _updateCameraShotStatus() {
    if (_selectedImages.isEmpty) {
      _isCameraShot = false;
    } else {
      _isCameraShot = _selectedImages.every((item) => item.isFromCamera);
    }
  }

  /// 构建表单卡片
  Widget _buildFormCard({required Widget child}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppTheme.spacingL),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        boxShadow: const [AppTheme.shadowMedium],
      ),
      child: child,
    );
  }

  /// 按钮可用性判断
  bool _canSubmit() {
    return _formKey.currentState?.validate() == true &&
        _selectedImages.isNotEmpty &&
        !_isSubmitting &&
        !_imageUploadManager.hasUploadingImages(_selectedImages);
  }

  /// 获取提交按钮文本
  String _getSubmitButtonText() {
    if (_isSubmitting) {
      return '发布中...';
    }
    if (_imageUploadManager.hasUploadingImages(_selectedImages)) {
      return '等待图片上传';
    }
    if (_selectedImages.isEmpty) {
      return '请添加图片';
    }
    if (_formKey.currentState?.validate() != true) {
      return '请完善信息';
    }

    // 根据是否实地验证显示不同文本
    return _isOnSite ? '发布钓点' : '发布钓点（非实地）';
  }

  /// 获取提交按钮图标
  Widget _getSubmitButtonIcon() {
    if (_isSubmitting ||
        _imageUploadManager.hasUploadingImages(_selectedImages)) {
      return const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2.5,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    }

    // 根据实地验证状态显示不同图标
    IconData iconData = _isOnSite ? Icons.location_on : Icons.publish_rounded;

    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Icon(iconData, color: Colors.white, size: 18),
    );
  }

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.5,
      minChildSize: 0.3,
      maxChildSize: 0.85,
      snap: true,
      snapSizes: const [0.3, 0.5, 0.85],
      builder: (context, scrollController) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Colors.white, Colors.grey.shade50],
            ),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 32,
                offset: const Offset(0, -8),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.04),
                blurRadius: 8,
                offset: const Offset(0, -2),
                spreadRadius: 0,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
            child:
                _isSubmitting
                    ? const Center(child: CircularProgressIndicator())
                    : Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 拖拽指示器 - 现代化设计
                          Center(
                            child: Container(
                              margin: const EdgeInsets.only(
                                top: 12,
                                bottom: 16,
                              ),
                              width: 48,
                              height: 5,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.grey.shade300,
                                    Colors.grey.shade400,
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(3),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.1),
                                    blurRadius: 4,
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          // 标题栏
                          Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 16,
                            ),
                            child: Row(
                              children: [
                                const Text(
                                  '添加钓点',
                                  style: TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                ),
                                const Spacer(),
                                IconButton(
                                  icon: const Icon(
                                    Icons.close,
                                    color: Colors.grey,
                                    size: 24,
                                  ),
                                  onPressed: widget.onClose,
                                ),
                              ],
                            ),
                          ),

                          // 坐标信息
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            child: Row(
                              children: [
                                LocationStatusWidget(
                                  size: 20,
                                  autoStart: true,
                                  timeoutSeconds: 11, // 与LocationService保持一致
                                  showSuccessSnackBar: true, // 启用成功提示
                                  onLocationUpdate: (data) {
                                    debugPrint(
                                      '🔍 [添加钓点] GPS状态更新: ${data.status}',
                                    );
                                    if (data.location != null) {
                                      debugPrint(
                                        '🔍 [添加钓点] GPS位置: ${data.location}',
                                      );
                                      // 更新实地验证状态
                                      setState(() {
                                        _isOnSite = true;
                                        _publishLocation = Position(
                                          latitude: data.location!.latitude,
                                          longitude: data.location!.longitude,
                                          timestamp: DateTime.now(),
                                          accuracy: 0,
                                          altitude: 0,
                                          altitudeAccuracy: 0,
                                          heading: 0,
                                          headingAccuracy: 0,
                                          speed: 0,
                                          speedAccuracy: 0,
                                        );
                                      });
                                    }
                                    if (data.errorMessage != null) {
                                      debugPrint(
                                        '🔍 [添加钓点] GPS错误: ${data.errorMessage}',
                                      );
                                    }
                                  },
                                ),
                                const SizedBox(width: 8),
                                const Text(
                                  '坐标位置',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.black87,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    '${widget.location.latitude.toStringAsFixed(6)}, ${widget.location.longitude.toStringAsFixed(6)}',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey.shade600,
                                    ),
                                    textAlign: TextAlign.right,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 16),

                          // 位置状态指示器 - 已移除旧的LocationVerificationWidget
                          // 新的LocationStatusWidget已经在坐标位置行中显示
                          const SizedBox(height: 24),

                          // 表单内容区域 - 优化布局
                          Expanded(
                            child: SingleChildScrollView(
                              controller: scrollController,
                              physics: const BouncingScrollPhysics(),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // 钓点名称
                                    _buildFormCard(
                                      child: SpotNameInput(
                                        controller: _nameController,
                                        location: widget.location,
                                        validator: (value) {
                                          if (value == null ||
                                              value.trim().isEmpty) {
                                            return '请输入钓点名称';
                                          }
                                          return null;
                                        },
                                      ),
                                    ),

                                    const SizedBox(height: AppTheme.spacingM),

                                    // 图片上传
                                    _buildFormCard(
                                      child: ImageUploadWidget(
                                        selectedImages: _selectedImages,
                                        onImagesAdded: _handleImagesAdded,
                                        onImageRemoved: _handleImageRemoved,
                                      ),
                                    ),

                                    const SizedBox(height: AppTheme.spacingM),

                                    // 钓点描述
                                    _buildFormCard(
                                      child: SpotDescriptionInput(
                                        controller: _descriptionController,
                                      ),
                                    ),

                                    const SizedBox(height: AppTheme.spacingM),

                                    // 水位、饵料和鱼种选择
                                    _buildFormCard(
                                      child: Column(
                                        children: [
                                          WaterLevelSelector(
                                            selectedWaterLevel:
                                                _selectedWaterLevel,
                                            onChanged:
                                                (value) => setState(
                                                  () =>
                                                      _selectedWaterLevel =
                                                          value,
                                                ),
                                          ),
                                          const SizedBox(
                                            height: AppTheme.spacingL,
                                          ),
                                          BaitSelector(
                                            selectedBait: _selectedBait,
                                            onChanged:
                                                (value) => setState(
                                                  () => _selectedBait = value,
                                                ),
                                          ),
                                          const SizedBox(
                                            height: AppTheme.spacingL,
                                          ),
                                          FishTypeSelector(
                                            selectedFishType: _selectedFishType,
                                            onChanged:
                                                (value) => setState(
                                                  () =>
                                                      _selectedFishType = value,
                                                ),
                                          ),
                                        ],
                                      ),
                                    ),

                                    const SizedBox(height: AppTheme.spacingM),

                                    // 验证状态
                                    _buildFormCard(
                                      child: VerificationStatusWidget(
                                        isOnSite: _isOnSite,
                                        isCameraShot: _isCameraShot,
                                        publishLocation:
                                            _publishLocation != null
                                                ? LocationVerificationService.createPublishLocationInfo(
                                                  widget.location,
                                                  _publishLocation!,
                                                )
                                                : null,
                                        hasImages: _selectedImages.isNotEmpty,
                                      ),
                                    ),

                                    const SizedBox(height: AppTheme.spacingM),

                                    // 可见性设置
                                    _buildFormCard(
                                      child: SpotVisibilitySelector(
                                        initialVisibility: _selectedVisibility,
                                        initialConditions:
                                            _visibilityConditions,
                                        onChanged: (visibility, conditions) {
                                          setState(() {
                                            _selectedVisibility = visibility;
                                            _visibilityConditions = conditions;
                                          });
                                        },
                                      ),
                                    ),

                                    const SizedBox(height: 32),

                                    // 发布按钮 - 现代化设计
                                    Container(
                                      width: double.infinity,
                                      height: 56,
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors:
                                              _isSubmitting ||
                                                      _imageUploadManager
                                                          .hasUploadingImages(
                                                            _selectedImages,
                                                          )
                                                  ? [
                                                    Colors.grey.shade300,
                                                    Colors.grey.shade400,
                                                  ]
                                                  : [
                                                    Colors.green.shade400,
                                                    Colors.green.shade600,
                                                  ],
                                        ),
                                        borderRadius: BorderRadius.circular(16),
                                        boxShadow:
                                            _isSubmitting ||
                                                    _imageUploadManager
                                                        .hasUploadingImages(
                                                          _selectedImages,
                                                        )
                                                ? []
                                                : [
                                                  BoxShadow(
                                                    color: Colors.green
                                                        .withValues(alpha: 0.4),
                                                    blurRadius: 16,
                                                    offset: const Offset(0, 4),
                                                  ),
                                                  BoxShadow(
                                                    color: Colors.white
                                                        .withValues(alpha: 0.2),
                                                    blurRadius: 8,
                                                    offset: const Offset(0, -2),
                                                  ),
                                                ],
                                      ),
                                      child: ElevatedButton(
                                        onPressed:
                                            _canSubmit() ? _handleSubmit : null,
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.transparent,
                                          shadowColor: Colors.transparent,
                                          foregroundColor: Colors.white,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(
                                              16,
                                            ),
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            _getSubmitButtonIcon(),
                                            const SizedBox(width: 8),
                                            Text(
                                              _getSubmitButtonText(),
                                              style: const TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                                letterSpacing: 0.5,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),

                                    // 底部安全区域
                                    const SizedBox(height: 24),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
          ),
        );
      },
    );
  }

  // 清空表单内容和状态
  void _clearForm() {
    // 清空文本输入框
    _nameController.clear();
    _descriptionController.clear();

    // 重置选择项为默认值
    setState(() {
      _selectedWaterLevel = '正常';
      _selectedBait = '商品饵';

      // 重置可见性设置
      _selectedVisibility = SpotVisibility.public;
      _visibilityConditions = null;

      // 清空图片列表
      _selectedImages.clear();

      // 重置加载状态
      _isSubmitting = false;
    });

    // 图片列表已在上面清空，无需额外操作

    debugPrint('✅ [表单清空] 表单内容已清空');
  }

  /// 保存照片记录到数据库
  Future<bool> _savePhotoRecord({
    required String spotId,
    required String userId,
    required ImageUploadItem imageItem,
    required int sortOrder,
  }) async {
    final recordStartTime = DateTime.now();
    try {
      debugPrint('⏰ [照片记录] 开始保存照片记录: ${recordStartTime.toIso8601String()}');
      debugPrint('🔍 [照片记录] 钓点ID: $spotId');
      debugPrint('🔍 [照片记录] 用户ID: $userId');
      debugPrint('🔍 [照片记录] 原图URL: ${imageItem.uploadedUrl}');
      debugPrint('🔍 [照片记录] 缩略图URL: ${imageItem.thumbnailUrl}');

      // 获取PocketBase客户端
      final pbStartTime = DateTime.now();
      debugPrint('⏰ [照片记录] 获取PocketBase客户端: ${pbStartTime.toIso8601String()}');
      final pb = PocketBaseConfig.instance.client;

      // 创建照片记录
      final createStartTime = DateTime.now();
      debugPrint(
        '⏰ [照片记录] 开始调用PocketBase API: ${createStartTime.toIso8601String()}',
      );
      final record = await pb
          .collection('spot_photos')
          .create(
            body: {
              'spot_id': spotId,
              'user_id': userId,
              'filename': path.basename(imageItem.file.path),
              'url': imageItem.uploadedUrl,
              'thumbnail_url': imageItem.thumbnailUrl ?? imageItem.uploadedUrl,
              'storage_path': imageItem.uploadedUrl, // 使用URL作为存储路径
              'thumbnail_path': imageItem.thumbnailUrl ?? imageItem.uploadedUrl,
              'type': 'normal',
              'description': null,
              'sort_order': sortOrder,
              'file_size': await imageItem.file.length(),
              'mime_type': 'image/jpeg',
              'is_camera_shot': imageItem.isFromCamera, // 添加照片来源信息
              'photo_source':
                  imageItem.isFromCamera ? 'camera' : 'gallery', // 添加照片来源字符串
            },
          );

      final createEndTime = DateTime.now();
      debugPrint(
        '⏰ [照片记录] PocketBase API调用完成: ${createEndTime.toIso8601String()}',
      );
      debugPrint(
        '⏰ [照片记录] PocketBase API耗时: ${createEndTime.difference(createStartTime).inMilliseconds}ms',
      );

      debugPrint('🔍 [照片记录] 照片来源: ${imageItem.isFromCamera ? "相机" : "相册"}');
      debugPrint('✅ [照片记录] 保存成功，记录ID: ${record.id}');

      final recordEndTime = DateTime.now();
      debugPrint(
        '⏰ [照片记录] 照片记录保存总耗时: ${recordEndTime.difference(recordStartTime).inMilliseconds}ms',
      );
      return true;
    } catch (e) {
      final errorTime = DateTime.now();
      debugPrint('⏰ [照片记录] 保存失败时间: ${errorTime.toIso8601String()}');
      debugPrint(
        '⏰ [照片记录] 失败前耗时: ${errorTime.difference(recordStartTime).inMilliseconds}ms',
      );
      debugPrint('❌ [照片记录] 保存失败: $e');
      return false;
    }
  }

  /// 关联已上传的图片到钓点（并行处理）
  Future<void> _associateUploadedImages(
    FishingSpot spot,
    dynamic user,
    List<ImageUploadItem> uploadedImages,
  ) async {
    final associationStartTime = DateTime.now();
    debugPrint(
      '⏰ [图片关联] 开始关联已上传的图片: ${associationStartTime.toIso8601String()}',
    );
    debugPrint('🔍 [图片关联] 钓点ID: ${spot.id}');
    debugPrint('🔍 [图片关联] 用户ID: ${user.id}');
    debugPrint('🔍 [图片关联] 图片数量: ${uploadedImages.length}');

    try {
      // 并行处理所有图片记录创建
      final futures =
          uploadedImages.asMap().entries.map((entry) {
            final index = entry.key;
            final imageItem = entry.value;

            return _savePhotoRecord(
              spotId: spot.id,
              userId: user.id,
              imageItem: imageItem,
              sortOrder: index,
            );
          }).toList();

      // 等待所有图片记录创建完成
      final waitStartTime = DateTime.now();
      debugPrint('⏰ [图片关联] 开始等待所有并行任务: ${waitStartTime.toIso8601String()}');
      final results = await Future.wait(futures);
      final waitEndTime = DateTime.now();
      debugPrint('⏰ [图片关联] 所有并行任务完成: ${waitEndTime.toIso8601String()}');
      debugPrint(
        '⏰ [图片关联] 并行任务总耗时: ${waitEndTime.difference(waitStartTime).inMilliseconds}ms',
      );

      // 检查结果
      final successCount = results.where((success) => success).length;
      final failureCount = results.length - successCount;

      if (failureCount > 0) {
        debugPrint('⚠️ [图片关联] $successCount 张照片记录保存成功，$failureCount 张失败');
      } else {
        debugPrint('✅ [图片关联] 所有 $successCount 张照片记录保存成功');
      }

      final associationEndTime = DateTime.now();
      debugPrint(
        '⏰ [图片关联] 图片关联总耗时: ${associationEndTime.difference(associationStartTime).inMilliseconds}ms',
      );
    } catch (e) {
      debugPrint('❌ [图片关联] 关联失败: $e');
      // 图片关联失败不影响钓点发布，只显示警告
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('钓点发布成功，但图片关联失败: $e')));
      }
    }
  }

  /// 异步关联图片（后台处理，不阻塞UI）
  void _associateUploadedImagesAsync(
    FishingSpot spot,
    dynamic user,
    List<ImageUploadItem> uploadedImages,
  ) {
    // 在后台异步处理，不阻塞UI
    Future.microtask(() async {
      try {
        await _associateUploadedImages(spot, user, uploadedImages);

        // 关联完成后显示成功提示
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('图片关联完成'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      } catch (e) {
        debugPrint('❌ [异步图片关联] 失败: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('图片关联失败: $e'),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    });
  }

  // 处理提交
  Future<void> _handleSubmit() async {
    final startTime = DateTime.now();
    debugPrint('⏰ [钓点发布] 开始时间: ${startTime.toIso8601String()}');

    if (!Services.auth.isLoggedIn) {
      // 跳转到登录页面，等待登录结果
      final result = await Navigator.pushNamed(context, '/login');
      // 如果登录成功，继续发布流程
      if (result == true && Services.auth.isLoggedIn) {
        // 登录成功后，递归调用自己继续发布
        await _handleSubmit();
      }
      return;
    }

    if (!_formKey.currentState!.validate()) {
      return;
    }

    // 检查照片必填项
    if (_selectedImages.isEmpty) {
      debugPrint('❌ [钓点创建] 照片为必填项');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请至少添加一张钓点照片'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // 检查是否有照片正在上传
    if (_imageUploadManager.hasUploadingImages(_selectedImages)) {
      debugPrint('⚠️ [钓点创建] 还有照片正在上传');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请等待照片上传完成'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final validationTime = DateTime.now();
    debugPrint('⏰ [钓点发布] 验证完成时间: ${validationTime.toIso8601String()}');
    debugPrint(
      '⏰ [钓点发布] 验证耗时: ${validationTime.difference(startTime).inMilliseconds}ms',
    );

    setState(() {
      _isSubmitting = true;
    });

    try {
      final submitStartTime = DateTime.now();
      debugPrint('⏰ [钓点发布] 提交开始时间: ${submitStartTime.toIso8601String()}');

      final user = Services.auth.currentUser;

      // 创建钓点对象
      String description = _descriptionController.text.trim();

      // 将水位和饵料信息添加到描述中
      List<String> additionalInfo = [];
      if (_selectedWaterLevel != '正常') {
        additionalInfo.add('水位：$_selectedWaterLevel');
      }
      if (_selectedBait != '商品饵') {
        additionalInfo.add('饵料：$_selectedBait');
      }

      if (additionalInfo.isNotEmpty) {
        if (description.isNotEmpty) {
          description += '\n\n';
        }
        description += additionalInfo.join('，');
      }

      final clientGeneratedId = const Uuid().v4();

      final spot = FishingSpot(
        id: clientGeneratedId,
        name: _nameController.text.trim(),
        description: description,
        location: {
          'lat': widget.location.latitude,
          'lon': widget.location.longitude,
        },
        userId: user?.id ?? '',
        userName: user?.username,
        spotType: 'freshwater', // 默认淡水钓点
        fishTypes: _selectedFishType, // 使用选中的鱼种
        spotEmoji: '🎣',
        fishEmoji: '🐟',
        visibility: _selectedVisibility,
        visibilityConditions: _visibilityConditions,
        visibilityUpdatedAt: DateTime.now(),
        created: DateTime.now(),
        updated: DateTime.now(),
      );

      final spotCreateStartTime = DateTime.now();
      debugPrint('⏰ [钓点创建] 开始创建钓点: ${spotCreateStartTime.toIso8601String()}');
      debugPrint('🔍 [钓点创建] 客户端生成的ID: $clientGeneratedId');
      debugPrint('🔍 [钓点创建] 钓点名称: ${spot.name}');
      debugPrint('🔍 [钓点创建] 可见性设置: ${_selectedVisibility.displayName}');
      if (_visibilityConditions != null) {
        debugPrint('🔍 [钓点创建] 可见性条件: $_visibilityConditions');
      }

      // 添加钓点
      final addedSpot = await Services.fishingSpot.addSpot(spot);

      final spotCreateEndTime = DateTime.now();
      debugPrint('⏰ [钓点创建] 钓点创建完成: ${spotCreateEndTime.toIso8601String()}');
      debugPrint(
        '⏰ [钓点创建] 钓点创建耗时: ${spotCreateEndTime.difference(spotCreateStartTime).inMilliseconds}ms',
      );

      if (addedSpot != null) {
        debugPrint('🔍 [钓点创建] 服务器返回的ID: ${addedSpot.id}');
        debugPrint('🔍 [钓点创建] ID是否相同: ${clientGeneratedId == addedSpot.id}');

        // 异步关联已上传的图片到钓点（不阻塞UI）
        final imageAssociationStartTime = DateTime.now();
        debugPrint(
          '⏰ [图片关联] 开始图片关联检查: ${imageAssociationStartTime.toIso8601String()}',
        );

        if (_selectedImages.isNotEmpty) {
          // 过滤出已上传成功的图片
          final uploadedImages =
              _selectedImages
                  .where(
                    (img) =>
                        img.uploadedUrl != null && img.uploadedUrl!.isNotEmpty,
                  )
                  .toList();

          debugPrint('⏰ [图片关联] 图片过滤完成，找到 ${uploadedImages.length} 张已上传图片');

          if (uploadedImages.isNotEmpty) {
            _associateUploadedImagesAsync(addedSpot, user, uploadedImages);
            debugPrint('🔄 [图片关联] 已启动后台图片关联任务');
          } else {
            debugPrint('⚠️ [图片关联] 没有已上传成功的图片需要关联');
          }
        }

        final imageAssociationEndTime = DateTime.now();
        debugPrint(
          '⏰ [图片关联] 图片关联检查完成: ${imageAssociationEndTime.toIso8601String()}',
        );
        debugPrint(
          '⏰ [图片关联] 图片关联检查耗时: ${imageAssociationEndTime.difference(imageAssociationStartTime).inMilliseconds}ms',
        );

        // 添加成功，调用回调
        widget.onSpotAdded(addedSpot);

        final uiUpdateStartTime = DateTime.now();
        debugPrint('⏰ [UI更新] 开始UI更新: ${uiUpdateStartTime.toIso8601String()}');

        if (mounted) {
          // 显示成功提示
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('钓点发布成功！')));

          // 清空表单内容
          _clearForm();
        }

        final uiUpdateEndTime = DateTime.now();
        debugPrint('⏰ [UI更新] UI更新完成: ${uiUpdateEndTime.toIso8601String()}');
        debugPrint(
          '⏰ [UI更新] UI更新耗时: ${uiUpdateEndTime.difference(uiUpdateStartTime).inMilliseconds}ms',
        );

        final totalEndTime = DateTime.now();
        debugPrint('⏰ [钓点发布] 总结束时间: ${totalEndTime.toIso8601String()}');
        debugPrint(
          '⏰ [钓点发布] 总耗时: ${totalEndTime.difference(startTime).inMilliseconds}ms',
        );
      } else {
        throw Exception('添加钓点失败');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('发布失败: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
}
