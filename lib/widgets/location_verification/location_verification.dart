// GPS位置验证模块
//
// 提供完整的GPS位置验证功能，包括：
// - 实时GPS监控
// - 位置状态显示
// - 距离计算和验证
// - 错误处理和重试
// - 可自定义的UI样式
// - 智能按钮组件
//
// 使用示例：
//
// ```dart
// // 基础位置验证Widget
// LocationVerificationWidget(
//   targetLocation: LatLng(29.5763, 120.8082),
//   thresholdMeters: 50.0,
//   onLocationUpdate: (data) {
//     print('位置状态: ${data.status}');
//     print('距离: ${data.distanceToTarget}米');
//     print('是否在范围内: ${data.isOnSite}');
//   },
// )
//
// // 智能位置验证按钮
// LocationVerificationButton(
//   targetLocation: LatLng(29.5763, 120.8082),
//   baseText: '发布钓点',
//   notOnSiteText: '发布钓点（非实地）',
//   onPressed: () {
//     // 处理按钮点击
//   },
//   onLocationUpdate: (data) {
//     // 处理位置更新
//   },
// )
//
// // 使用控制器进行程序化控制
// final controller = LocationVerificationController();
// controller.configure(
//   targetLocation: LatLng(29.5763, 120.8082),
//   thresholdMeters: 50.0,
// );
// controller.setOnLocationUpdate((data) {
//   // 处理位置更新
// });
// await controller.startVerification();
// ```



// 核心组件
export 'location_verification_widget.dart';
export 'location_verification_controller.dart';
export 'location_verification_button.dart';

// 数据类型
export 'location_verification_widget.dart' show 
    LocationVerificationStatus,
    LocationVerificationData,
    LocationVerificationStyle;

export 'location_verification_button.dart' show
    LocationVerificationButtonStyle;
