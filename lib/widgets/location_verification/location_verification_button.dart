import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import 'location_verification_widget.dart';
import 'location_verification_controller.dart';

/// 位置验证按钮样式
class LocationVerificationButtonStyle {
  final Color? backgroundColor;
  final Color? foregroundColor;
  final EdgeInsets padding;
  final BorderRadius borderRadius;
  final double elevation;
  final TextStyle? textStyle;

  const LocationVerificationButtonStyle({
    this.backgroundColor,
    this.foregroundColor,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    this.borderRadius = const BorderRadius.all(Radius.circular(8)),
    this.elevation = 2.0,
    this.textStyle,
  });
}

/// 智能位置验证按钮
/// 
/// 根据GPS状态自动调整按钮文本、图标和可用性
class LocationVerificationButton extends StatefulWidget {
  /// 目标位置
  final LatLng targetLocation;
  
  /// 验证距离阈值（米）
  final double thresholdMeters;
  
  /// 按钮点击回调
  final VoidCallback? onPressed;
  
  /// 位置更新回调
  final Function(LocationVerificationData data)? onLocationUpdate;
  
  /// 基础按钮文本
  final String baseText;
  
  /// 非实地时的按钮文本
  final String notOnSiteText;
  
  /// 是否GPS状态影响按钮可用性
  final bool gpsAffectsAvailability;
  
  /// 其他可用性条件
  final bool Function()? additionalAvailabilityCheck;
  
  /// 按钮样式
  final LocationVerificationButtonStyle style;
  
  /// 是否显示位置图标
  final bool showLocationIcon;

  const LocationVerificationButton({
    super.key,
    required this.targetLocation,
    required this.onPressed,
    this.thresholdMeters = 50.0,
    this.onLocationUpdate,
    this.baseText = '确认',
    this.notOnSiteText = '确认（非实地）',
    this.gpsAffectsAvailability = false,
    this.additionalAvailabilityCheck,
    this.style = const LocationVerificationButtonStyle(),
    this.showLocationIcon = true,
  });

  @override
  State<LocationVerificationButton> createState() => _LocationVerificationButtonState();
}

class _LocationVerificationButtonState extends State<LocationVerificationButton> {
  late LocationVerificationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = LocationVerificationController();
    _controller.configure(
      targetLocation: widget.targetLocation,
      thresholdMeters: widget.thresholdMeters,
    );
    _controller.setOnLocationUpdate(_handleLocationUpdate);
    _controller.addListener(_onControllerUpdate);
    _controller.startVerification();
  }

  @override
  void dispose() {
    _controller.removeListener(_onControllerUpdate);
    _controller.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(LocationVerificationButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 如果目标位置或阈值发生变化，重新配置控制器
    if (widget.targetLocation != oldWidget.targetLocation ||
        widget.thresholdMeters != oldWidget.thresholdMeters) {
      _controller.configure(
        targetLocation: widget.targetLocation,
        thresholdMeters: widget.thresholdMeters,
      );
      _controller.startVerification();
    }
  }

  void _handleLocationUpdate(LocationVerificationData data) {
    setState(() {
      // 触发UI更新
    });

    // 通知外部回调
    if (widget.onLocationUpdate != null) {
      widget.onLocationUpdate!(data);
    }
  }

  void _onControllerUpdate() {
    setState(() {
      // 触发UI更新
    });
  }

  /// 判断按钮是否可用
  bool _isButtonEnabled() {
    // 检查额外的可用性条件
    if (widget.additionalAvailabilityCheck != null) {
      if (!widget.additionalAvailabilityCheck!()) {
        return false;
      }
    }
    
    // 如果GPS状态影响可用性
    if (widget.gpsAffectsAvailability) {
      return _controller.status != LocationVerificationStatus.loading &&
             _controller.status != LocationVerificationStatus.error;
    }
    
    // 默认情况下，GPS状态不影响按钮可用性
    return widget.onPressed != null;
  }

  /// 获取按钮文本
  String _getButtonText() {
    if (_controller.isLoading) {
      return '获取位置中...';
    }
    
    if (_controller.hasError) {
      return widget.baseText;
    }
    
    if (_controller.status == LocationVerificationStatus.notOnSite) {
      return widget.notOnSiteText;
    }
    
    return widget.baseText;
  }

  /// 获取按钮图标
  Widget? _getButtonIcon() {
    if (!widget.showLocationIcon) return null;
    
    if (_controller.isLoading) {
      return const SizedBox(
        width: 16,
        height: 16,
        child: CircularProgressIndicator(strokeWidth: 2),
      );
    }
    
    IconData iconData;
    Color? iconColor;
    
    switch (_controller.status) {
      case LocationVerificationStatus.verified:
        iconData = Icons.location_on;
        iconColor = Colors.green;
        break;
      case LocationVerificationStatus.notOnSite:
        iconData = Icons.location_off;
        iconColor = Colors.orange;
        break;
      case LocationVerificationStatus.error:
        iconData = Icons.location_disabled;
        iconColor = Colors.grey;
        break;
      case LocationVerificationStatus.loading:
        iconData = Icons.location_searching;
        iconColor = Colors.blue;
        break;
    }
    
    return Icon(
      iconData,
      size: 16,
      color: iconColor,
    );
  }

  @override
  Widget build(BuildContext context) {
    final isEnabled = _isButtonEnabled();
    final buttonIcon = _getButtonIcon();
    
    return ElevatedButton(
      onPressed: isEnabled ? widget.onPressed : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: widget.style.backgroundColor,
        foregroundColor: widget.style.foregroundColor,
        padding: widget.style.padding,
        shape: RoundedRectangleBorder(
          borderRadius: widget.style.borderRadius,
        ),
        elevation: widget.style.elevation,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (buttonIcon != null) ...[
            buttonIcon,
            const SizedBox(width: 8),
          ],
          Text(
            _getButtonText(),
            style: widget.style.textStyle,
          ),
        ],
      ),
    );
  }
}

/// 简化的位置验证状态指示器
class LocationVerificationIndicator extends StatefulWidget {
  /// 目标位置
  final LatLng targetLocation;
  
  /// 验证距离阈值（米）
  final double thresholdMeters;
  
  /// 位置更新回调
  final Function(LocationVerificationData data)? onLocationUpdate;
  
  /// 是否显示距离
  final bool showDistance;
  
  /// 是否允许重试
  final bool allowRetry;
  
  /// 自定义样式
  final LocationVerificationStyle style;

  const LocationVerificationIndicator({
    super.key,
    required this.targetLocation,
    this.thresholdMeters = 50.0,
    this.onLocationUpdate,
    this.showDistance = true,
    this.allowRetry = true,
    this.style = const LocationVerificationStyle(),
  });

  @override
  State<LocationVerificationIndicator> createState() => _LocationVerificationIndicatorState();
}

class _LocationVerificationIndicatorState extends State<LocationVerificationIndicator> {
  @override
  Widget build(BuildContext context) {
    return LocationVerificationWidget(
      targetLocation: widget.targetLocation,
      thresholdMeters: widget.thresholdMeters,
      onLocationUpdate: widget.onLocationUpdate,
      showDistance: widget.showDistance,
      allowRetry: widget.allowRetry,
      style: widget.style,
    );
  }
}
