import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:latlong2/latlong.dart';
import '../config/pocketbase_config.dart';
import '../models/fishing_spot.dart';
import '../models/user.dart';
import '../models/spot_visibility.dart';
import '../models/spot_photo.dart';
import 'auth_service_new.dart';
import 'service_locator.dart';
import 'location_service.dart';

/// 钓点管理服务
///
/// 职责：
/// - 钓点CRUD操作
/// - 钓点搜索和筛选
/// - 地理位置查询
/// - 照片上传管理
class FishingSpotService {
  // 单例模式
  static final FishingSpotService _instance = FishingSpotService._internal();
  factory FishingSpotService() => _instance;
  FishingSpotService._internal();

  // 依赖的服务
  final AuthService _authService = AuthService();

  // 本地存储键
  static const String _spotsStorageKey = 'fishing_spots_cache';

  // 钓点数据缓存
  List<FishingSpot> _spotsCache = [];
  DateTime _lastAllSpotsLoadTime = DateTime.now().subtract(
    const Duration(hours: 1),
  );

  // 区域缓存
  final Map<String, List<FishingSpot>> _regionCache = {};
  final Map<String, DateTime> _regionCacheTime = {};

  static const int _cacheDurationMinutes = 5;

  /// 获取当前登录用户
  User? get currentUser => _authService.currentUser;

  /// 初始化钓点服务
  Future<void> initialize() async {
    debugPrint('初始化钓点服务');
    await _loadSpotsFromLocal();
  }

  // ==================== 钓点CRUD操作 ====================

  /// 获取所有钓点
  Future<List<FishingSpot>> getAllSpots() async {
    final now = DateTime.now();

    // 检查缓存是否有效
    if (_spotsCache.isNotEmpty &&
        now.difference(_lastAllSpotsLoadTime).inMinutes <
            _cacheDurationMinutes) {
      debugPrint('使用缓存的钓点数据');
      return _spotsCache;
    }

    try {
      // 从 PocketBase 获取钓点数据
      final records = await pb
          .collection('fishing_spots')
          .getFullList(
            sort: '-created',
            expand: 'user_id,spot_photos(spot_id),comments(spot_id).user_id',
          );

      // 转换为FishingSpot对象并更新缓存
      _spotsCache = _convertRecordsToSpots(records);
      _lastAllSpotsLoadTime = now;

      // 清理区域缓存，因为全局数据已更新
      _regionCache.clear();
      _regionCacheTime.clear();

      // 异步保存到本地
      _saveSpotsToLocal().catchError((e) => debugPrint('保存钓点数据失败: $e'));

      return _spotsCache;
    } catch (e) {
      debugPrint('获取钓点失败: $e');

      // 如果API调用失败但有缓存，返回缓存
      if (_spotsCache.isNotEmpty) {
        debugPrint('使用过期的钓点缓存数据');
        return _spotsCache;
      }

      // 如果没有缓存，尝试从本地加载
      await _loadSpotsFromLocal();
      return _spotsCache;
    }
  }

  /// 根据ID获取钓点
  Future<FishingSpot?> getSpotById(String id) async {
    try {
      final record = await pb
          .collection('fishing_spots')
          .getOne(
            id,
            expand: 'user_id,spot_photos(spot_id),comments(spot_id).user_id',
          );

      final spots = _convertRecordsToSpots([record]);
      return spots.isNotEmpty ? spots.first : null;
    } catch (e) {
      debugPrint('获取钓点详情失败: $e');
      // 尝试从本地缓存获取
      try {
        return _spotsCache.firstWhere((spot) => spot.id == id);
      } catch (e) {
        return null;
      }
    }
  }

  /// 添加新钓点
  Future<FishingSpot?> addSpot(
    FishingSpot spot, {
    Map<String, dynamic>? publishLocation,
  }) async {
    try {
      // 检查用户是否已登录
      if (currentUser == null) {
        throw Exception('用户未登录');
      }

      final userId = currentUser!.id;
      final locationService = LocationService();

      debugPrint('🔍 [钓点服务] 准备创建钓点');
      debugPrint('🔍 [钓点服务] 客户端传入的ID: ${spot.id}');
      debugPrint('🔍 [钓点服务] 钓点名称: ${spot.name}');
      debugPrint('🔍 [钓点服务] 用户ID: $userId');

      // 获取发布位置（如果没有提供，则获取当前位置）
      Map<String, dynamic>? finalPublishLocation = publishLocation;
      if (finalPublishLocation == null) {
        try {
          finalPublishLocation =
              await locationService.getCurrentLocationAsGeoPointAsync();
          debugPrint('🔍 [钓点服务] 获取到发布位置: $finalPublishLocation');
        } catch (e) {
          debugPrint('⚠️ [钓点服务] 获取发布位置失败: $e');
        }
      }

      // 检查是否实地发布（钓点位置与发布位置距离≤50米）
      bool isOnSite = false;
      DateTime? onSiteVerifiedAt;

      if (spot.location != null && finalPublishLocation != null) {
        isOnSite = locationService.isWithinOnSiteRange(
          spot.location!,
          finalPublishLocation,
        );
        if (isOnSite) {
          onSiteVerifiedAt = DateTime.now();
          debugPrint('✅ [钓点服务] 实地发布验证通过');
        } else {
          debugPrint('❌ [钓点服务] 不在实地范围内');
        }
      }

      // 将钓点数据插入到 PocketBase
      final record = await pb
          .collection('fishing_spots')
          .create(
            body: {
              'user_id': userId,
              'name': spot.name,
              'location': spot.location,
              'publish_location': finalPublishLocation,
              'is_on_site': isOnSite,
              'on_site_verified_at': onSiteVerifiedAt?.toIso8601String(),
              'description': spot.description,
              'address': spot.address,
              'spot_type': spot.spotType,
              'fish_types': spot.fishTypes,
              'status': spot.status,
              'visibility': spot.visibility.value,
              'visibility_conditions':
                  spot.visibilityConditions != null
                      ? jsonEncode(spot.visibilityConditions)
                      : null,
              'visibility_updated_at':
                  spot.visibilityUpdatedAt.toIso8601String(),
            },
          );

      debugPrint('🔍 [钓点服务] PocketBase创建成功');
      debugPrint('🔍 [钓点服务] PocketBase生成的ID: ${record.id}');
      debugPrint('🔍 [钓点服务] ID是否相同: ${spot.id == record.id}');

      // 如果有照片，上传照片
      if (spot.photoUrls.isNotEmpty) {
        debugPrint('🔍 [钓点服务] 准备上传 ${spot.photoUrls.length} 张照片');
        for (final photoPath in spot.photoUrls) {
          await addPhotoToSpot(record.id, photoPath, false);
        }
      }

      // 如果有全景照片，上传全景照片
      if (spot.panoramaPhotoUrl != null) {
        debugPrint('🔍 [钓点服务] 准备上传全景照片');
        await addPhotoToSpot(record.id, spot.panoramaPhotoUrl!, true);
      }

      // 获取完整的钓点数据
      debugPrint('🔍 [钓点服务] 获取完整钓点数据: ${record.id}');
      final newSpot = await getSpotById(record.id);

      if (newSpot != null) {
        debugPrint('🔍 [钓点服务] 获取完整数据成功');
        debugPrint('🔍 [钓点服务] 最终返回的ID: ${newSpot.id}');

        // 更新缓存
        _spotsCache.insert(0, newSpot);
        await _saveSpotsToLocal();
      } else {
        debugPrint('❌ [钓点服务] 获取完整数据失败');
      }

      debugPrint('✅ [钓点服务] 添加钓点成功: ${record.id}');
      return newSpot;
    } catch (e) {
      debugPrint('添加钓点失败: $e');
      return null;
    }
  }

  /// 更新钓点
  Future<bool> updateSpot(FishingSpot spot) async {
    try {
      // 检查用户是否已登录
      if (currentUser == null) {
        throw Exception('用户未登录');
      }

      // 先检查钓点是否属于当前用户
      final existingRecord = await pb
          .collection('fishing_spots')
          .getOne(spot.id);

      if (existingRecord.data['user_id'] != currentUser!.id) {
        throw Exception('无权限更新此钓点');
      }

      // 更新钓点数据
      await pb
          .collection('fishing_spots')
          .update(
            spot.id,
            body: {
              'name': spot.name,
              'location': spot.location,
              'description': spot.description,
              'address': spot.address,
              'spot_type': spot.spotType,
              'fish_types': spot.fishTypes,
              'status': spot.status,
              'visibility': spot.visibility.value,
              'visibility_conditions':
                  spot.visibilityConditions != null
                      ? jsonEncode(spot.visibilityConditions)
                      : null,
              'visibility_updated_at':
                  spot.visibilityUpdatedAt.toIso8601String(),
            },
          );

      // 更新缓存
      final index = _spotsCache.indexWhere((s) => s.id == spot.id);
      if (index != -1) {
        _spotsCache[index] = spot;
        await _saveSpotsToLocal();
      }

      debugPrint('更新钓点成功: ${spot.id}');
      return true;
    } catch (e) {
      debugPrint('更新钓点失败: $e');
      return false;
    }
  }

  /// 删除钓点
  Future<bool> deleteSpot(String spotId) async {
    try {
      // 检查用户是否已登录
      if (currentUser == null) {
        throw Exception('用户未登录');
      }

      // 先检查钓点是否属于当前用户
      final existingRecord = await pb
          .collection('fishing_spots')
          .getOne(spotId);

      if (existingRecord.data['user_id'] != currentUser!.id) {
        throw Exception('无权限删除此钓点');
      }

      // 删除钓点
      await pb.collection('fishing_spots').delete(spotId);

      // 更新缓存
      _spotsCache.removeWhere((spot) => spot.id == spotId);
      await _saveSpotsToLocal();

      debugPrint('删除钓点成功: $spotId');
      return true;
    } catch (e) {
      debugPrint('删除钓点失败: $e');
      return false;
    }
  }

  // ==================== 可见性过滤 ====================

  /// 获取用户可见的钓点列表
  Future<List<FishingSpot>> getVisibleSpots({
    LatLng? center,
    double? radiusKm,
    int page = 1,
    int perPage = 30,
  }) async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        // 未登录用户只能看到公开钓点
        return await _getPublicSpots(
          center: center,
          radiusKm: radiusKm,
          page: page,
          perPage: perPage,
        );
      }

      // 构建基础过滤条件
      String filter = _buildVisibilityFilter(currentUser.id);

      // 添加地理位置过滤（使用geoDistance函数）
      if (center != null && radiusKm != null) {
        filter +=
            ' && geoDistance(location.lon, location.lat, ${center.longitude}, ${center.latitude}) <= $radiusKm';
        debugPrint(
          '🔍 [钓点查询] 地理位置过滤条件: geoDistance(location.lon, location.lat, ${center.longitude}, ${center.latitude}) <= $radiusKm',
        );
      }

      late final dynamic records;
      try {
        records = await pb
            .collection('fishing_spots')
            .getList(
              page: page,
              perPage: perPage,
              filter: filter,
              sort: '-created',
              expand: 'user_id,spot_photos(spot_id)',
            );
      } catch (e) {
        debugPrint('❌ [钓点服务] 查询钓点失败，尝试使用简化查询: $e');
        // 如果复杂查询失败，尝试使用简化查询
        records = await pb
            .collection('fishing_spots')
            .getList(
              page: page,
              perPage: perPage,
              filter: "visibility = 'PUBLIC'",
              sort: '-created',
              expand: 'user_id,spot_photos(spot_id)',
            );
      }

      final spots = _convertRecordsToSpots(records.items);

      // 进一步过滤条件可见的钓点
      final visibleSpots = <FishingSpot>[];
      for (final spot in spots) {
        if (await Services.spotVisibility.canUserViewSpot(
          currentUser.id,
          spot,
        )) {
          visibleSpots.add(spot);
        }
      }

      return visibleSpots;
    } catch (e) {
      debugPrint('获取可见钓点失败: $e');
      return [];
    }
  }

  /// 获取公开钓点（未登录用户）
  Future<List<FishingSpot>> _getPublicSpots({
    LatLng? center,
    double? radiusKm,
    int page = 1,
    int perPage = 30,
  }) async {
    String filter = "visibility = 'PUBLIC'";

    if (center != null && radiusKm != null) {
      filter +=
          ' && geoDistance(location.lon, location.lat, ${center.longitude}, ${center.latitude}) <= $radiusKm';
      debugPrint(
        '🔍 [公开钓点查询] 地理位置过滤条件: geoDistance(location.lon, location.lat, ${center.longitude}, ${center.latitude}) <= $radiusKm',
      );
    }

    final records = await pb
        .collection('fishing_spots')
        .getList(
          page: page,
          perPage: perPage,
          filter: filter,
          sort: '-created',
          expand: 'user_id,spot_photos(spot_id)',
        );

    return _convertRecordsToSpots(records.items);
  }

  /// 构建可见性过滤条件
  String _buildVisibilityFilter(String userId) {
    if (userId.isEmpty) {
      // 未登录用户只能看到公开钓点
      return "visibility = 'PUBLIC'";
    } else {
      // 已登录用户可以看到公开钓点和自己的私有钓点
      return "(visibility = 'PUBLIC' || user_id = '$userId')";
    }
  }

  // ==================== 搜索和筛选 ====================

  /// 搜索钓点
  Future<List<FishingSpot>> searchSpots(String query, {int limit = 20}) async {
    try {
      final records = await pb
          .collection('fishing_spots')
          .getList(
            page: 1,
            perPage: limit,
            filter:
                'name ~ "$query" || description ~ "$query" || address ~ "$query"',
            expand: 'user_id,spot_photos(spot_id)',
          );

      return _convertRecordsToSpots(records.items);
    } catch (e) {
      debugPrint('搜索钓点失败: $e');
      return [];
    }
  }

  /// 获取钓点列表（用于测试和简单查询）
  Future<List<FishingSpot>> getSpots({int page = 1, int perPage = 20}) async {
    try {
      final currentUser = Services.auth.currentUser;
      final userId = currentUser?.id ?? '';

      // 构建过滤条件
      final filter = _buildVisibilityFilter(userId);

      late final dynamic records;
      try {
        records = await pb
            .collection('fishing_spots')
            .getList(
              page: page,
              perPage: perPage,
              filter: filter,
              sort: '-created',
              expand: 'user_id,spot_photos(spot_id)',
            );
      } catch (e) {
        debugPrint('❌ [钓点服务] 查询钓点失败，尝试使用简化查询: $e');
        // 如果复杂查询失败，尝试使用简化查询
        records = await pb
            .collection('fishing_spots')
            .getList(
              page: page,
              perPage: perPage,
              filter: "visibility = 'PUBLIC'",
              sort: '-created',
              expand: 'user_id,spot_photos(spot_id)',
            );
      }

      final spots = _convertRecordsToSpots(records.items);

      debugPrint('✅ [钓点服务] 查询到 ${spots.length} 个钓点');
      return spots;
    } catch (e) {
      debugPrint('❌ [钓点服务] 获取钓点失败: $e');
      return [];
    }
  }

  /// 根据地理范围获取钓点
  Future<List<FishingSpot>> getSpotsInBounds({
    required double minLat,
    required double maxLat,
    required double minLng,
    required double maxLng,
    int limit = 100,
  }) async {
    final cacheKey = '${minLat}_${maxLat}_${minLng}_$maxLng';
    final now = DateTime.now();

    // 检查区域缓存
    if (_regionCache.containsKey(cacheKey) &&
        _regionCacheTime.containsKey(cacheKey) &&
        now.difference(_regionCacheTime[cacheKey]!).inMinutes <
            _cacheDurationMinutes) {
      debugPrint('使用区域缓存数据');
      return _regionCache[cacheKey]!;
    }

    try {
      // 构建包含可见性过滤的查询条件
      final currentUserId = Services.auth.currentUser?.id ?? '';
      final visibilityFilter = _buildVisibilityFilter(currentUserId);
      // 计算中心点和半径
      final centerLat = (minLat + maxLat) / 2;
      final centerLng = (minLng + maxLng) / 2;
      // 计算对角线距离作为半径（粗略估算）
      const double kmPerDegree = 111.0;
      final radiusKm =
          sqrt(
            pow((maxLat - minLat) * kmPerDegree, 2) +
                pow((maxLng - minLng) * kmPerDegree, 2),
          ) /
          2;

      final locationFilter =
          'geoDistance(location.lon, location.lat, $centerLng, $centerLat) <= $radiusKm';
      final combinedFilter = '$visibilityFilter && ($locationFilter)';

      debugPrint(
        '🔍 [区域查询] 地理位置过滤条件: geoDistance(location.lon, location.lat, $centerLng, $centerLat) <= $radiusKm',
      );

      final records = await pb
          .collection('fishing_spots')
          .getList(
            page: 1,
            perPage: limit,
            filter: combinedFilter,
            expand: 'user_id,spot_photos(spot_id)',
          );

      final spots = _convertRecordsToSpots(records.items);

      // 进一步过滤条件可见的钓点
      final visibleSpots = <FishingSpot>[];
      for (final spot in spots) {
        if (await Services.spotVisibility.canUserViewSpot(
          currentUserId,
          spot,
        )) {
          visibleSpots.add(spot);
        }
      }

      debugPrint(
        '🔍 [钓点加载] 地理范围内原始钓点: ${spots.length}，可见钓点: ${visibleSpots.length}',
      );

      // 更新区域缓存
      _regionCache[cacheKey] = visibleSpots;
      _regionCacheTime[cacheKey] = now;

      return visibleSpots;
    } catch (e) {
      debugPrint('获取区域钓点失败: $e');
      return _regionCache[cacheKey] ?? [];
    }
  }

  /// 获取用户发布的钓点
  Future<List<FishingSpot>> getUserSpots(String userId) async {
    try {
      final records = await pb
          .collection('fishing_spots')
          .getList(
            filter: 'user_id = "$userId"',
            sort: '-created',
            expand: 'user_id,spot_photos(spot_id)',
          );

      return _convertRecordsToSpots(records.items);
    } catch (e) {
      debugPrint('获取用户钓点失败: $e');
      return [];
    }
  }

  // ==================== 照片管理 ====================

  /// 上传照片并添加到钓点
  Future<String?> addPhotoToSpot(
    String spotId,
    String photoPath,
    bool isPanorama, {
    String photoSource = 'gallery',
    bool isCameraShot = false,
  }) async {
    try {
      // 检查用户是否已登录
      if (currentUser == null) {
        throw Exception('用户未登录');
      }

      // 上传文件到 PocketBase
      final file = File(photoPath);
      final formData = <String, dynamic>{
        'spot_id': spotId,
        'user_id': currentUser!.id,
        'is_panorama': isPanorama,
        'photo_source': photoSource,
        'is_camera_shot': isCameraShot,
        'photo': file,
      };

      // 创建照片记录并上传文件
      final record = await pb.collection('spot_photos').create(body: formData);

      // 获取文件URL
      final photoUrl = pb.files.getUrl(record, record.data['photo']).toString();

      debugPrint('上传照片成功: $photoUrl');
      return photoUrl;
    } catch (e) {
      debugPrint('上传照片失败: $e');
      return null;
    }
  }

  /// 删除钓点照片
  Future<bool> deleteSpotPhoto(String photoId) async {
    try {
      // 检查用户是否已登录
      if (currentUser == null) {
        throw Exception('用户未登录');
      }

      // 检查照片是否属于当前用户
      final photo = await pb.collection('spot_photos').getOne(photoId);
      if (photo.data['user_id'] != currentUser!.id) {
        throw Exception('无权限删除此照片');
      }

      await pb.collection('spot_photos').delete(photoId);

      debugPrint('删除照片成功: $photoId');
      return true;
    } catch (e) {
      debugPrint('删除照片失败: $e');
      return false;
    }
  }

  // ==================== 私有方法 ====================

  /// 将PocketBase记录转换为FishingSpot对象
  List<FishingSpot> _convertRecordsToSpots(List<dynamic> records) {
    return records.map((record) {
      // 解析可见性设置
      SpotVisibility visibility = SpotVisibility.public;
      Map<String, dynamic>? visibilityConditions;
      DateTime visibilityUpdatedAt = DateTime.now();

      try {
        // 解析可见性字段
        if (record.data['visibility'] != null &&
            record.data['visibility'] != '') {
          visibility = SpotVisibility.fromString(record.data['visibility']);
        } else {
          // 默认为公开
          visibility = SpotVisibility.public;
        }

        // 解析可见性条件
        if (record.data['visibility_conditions'] != null) {
          final conditionsData = record.data['visibility_conditions'];
          if (conditionsData is String) {
            visibilityConditions = jsonDecode(conditionsData);
          } else if (conditionsData is Map) {
            visibilityConditions = Map<String, dynamic>.from(conditionsData);
          }
        }

        // 解析可见性更新时间
        if (record.data['visibility_updated_at'] != null) {
          try {
            final dateStr = record.data['visibility_updated_at'].toString();
            if (dateStr.isNotEmpty && dateStr != 'null' && dateStr != '') {
              visibilityUpdatedAt = DateTime.parse(dateStr);
            }
          } catch (dateError) {
            debugPrint('⚠️ [钓点转换] 解析可见性更新时间失败: $dateError');
            // 使用默认时间
          }
        }
      } catch (e) {
        debugPrint('⚠️ [钓点转换] 解析可见性设置失败: $e');
        // 使用默认值
      }

      // 提取关联的用户信息
      String? userName;
      try {
        if (record.expand != null && record.expand['user_id'] != null) {
          final userData = record.expand['user_id'];
          // debugPrint('🔍 [用户信息] 原始用户数据类型: ${userData.runtimeType}');

          // 处理不同的数据格式
          dynamic userRecord;
          if (userData is List && userData.isNotEmpty) {
            // 如果是列表，取第一个元素
            userRecord = userData.first;
            // debugPrint('🔍 [用户信息] 从列表中提取第一个用户记录');
          } else {
            // 如果是单个对象，直接使用
            userRecord = userData;
          }

          if (userRecord is Map) {
            // 直接是Map格式
            userName = userRecord['username'];
            // debugPrint('🔍 [用户信息] 从Map提取用户名: $userName');
          } else {
            // 可能是RecordModel对象
            try {
              userName = userRecord.data['username'];
              // debugPrint('🔍 [用户信息] 从RecordModel.data提取用户名: $userName');
            } catch (e) {
              debugPrint('⚠️ [用户信息] RecordModel.data解析失败: $e');
              // 尝试直接访问属性
              try {
                userName = userRecord.username;
                // debugPrint('🔍 [用户信息] 从RecordModel属性提取用户名: $userName');
              } catch (e2) {
                debugPrint('⚠️ [用户信息] RecordModel属性访问失败: $e2');
              }
            }
          }

          // 如果仍然没有获取到用户名，使用用户ID作为后备
          if (userName == null || userName.isEmpty) {
            userName = record.data['user_id'];
            debugPrint('⚠️ [用户信息] 使用用户ID作为后备: $userName');
          }
        }
      } catch (e) {
        debugPrint('❌ [用户信息] 提取用户名失败: $e');
        userName = record.data['user_id']; // 使用用户ID作为后备
      }

      return FishingSpot(
        id: record.id,
        name: record.data['name'],
        description: record.data['description'] ?? '',
        location: record.data['location'],
        userId: record.data['user_id'],
        userName: userName,
        address: record.data['address'],
        spotType: record.data['spot_type'],
        fishTypes: record.data['fish_types'],
        status: record.data['status'] ?? 'active',
        created: DateTime.parse(record.data['created']),
        updated: DateTime.parse(record.data['updated']),
        visibility: visibility,
        visibilityConditions: visibilityConditions,
        visibilityUpdatedAt: visibilityUpdatedAt,
      );
    }).toList();
  }

  /// 从本地存储加载钓点数据
  Future<void> _loadSpotsFromLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final spotsJson = prefs.getString(_spotsStorageKey);

      if (spotsJson != null) {
        final List<dynamic> decodedList = jsonDecode(spotsJson);
        _spotsCache =
            decodedList.map((item) => FishingSpot.fromJson(item)).toList();
        debugPrint('从本地加载了 ${_spotsCache.length} 个钓点');
      }
    } catch (e) {
      debugPrint('从本地加载钓点数据失败: $e');
      _spotsCache = [];
    }
  }

  /// 保存钓点数据到本地存储
  Future<void> _saveSpotsToLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final spotsJson = jsonEncode(
        _spotsCache.map((spot) => spot.toJson()).toList(),
      );
      await prefs.setString(_spotsStorageKey, spotsJson);
      debugPrint('保存了 ${_spotsCache.length} 个钓点到本地');
    } catch (e) {
      debugPrint('保存钓点数据到本地失败: $e');
    }
  }

  /// 清除缓存
  void clearCache() {
    _spotsCache.clear();
    _regionCache.clear();
    _regionCacheTime.clear();
    _lastAllSpotsLoadTime = DateTime.now().subtract(const Duration(hours: 1));
  }

  /// 清除所有缓存（包括本地存储）
  ///
  /// 在用户切换时调用，确保不同用户之间的数据隔离
  Future<void> clearAllCache() async {
    try {
      // 清除内存缓存
      clearCache();
      debugPrint('🧹 [钓点服务] 已清除内存缓存');

      // 清除本地存储缓存
      await _clearLocalCache();
      debugPrint('🧹 [钓点服务] 已清除本地存储缓存');

      debugPrint('✅ [钓点服务] 所有缓存已清理完成');
    } catch (e) {
      debugPrint('❌ [钓点服务] 清理缓存时出错: $e');
    }
  }

  /// 清除本地存储缓存
  Future<void> _clearLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_spotsStorageKey);
      debugPrint('🧹 [钓点服务] 已清除本地存储的钓点数据');
    } catch (e) {
      debugPrint('❌ [钓点服务] 清除本地存储失败: $e');
    }
  }

  /// 刷新缓存
  Future<void> refreshCache() async {
    clearCache();
    await getAllSpots();
  }

  /// 获取钓点照片列表
  Future<List<SpotPhoto>> getSpotPhotos(String spotId) async {
    try {
      final records = await pb
          .collection('spot_photos')
          .getFullList(
            filter: 'spot_id = "$spotId"',
            sort: 'sort_order,created',
          );

      return records.map((record) => SpotPhoto.fromJson(record.toJson())).toList();
    } catch (e) {
      debugPrint('获取钓点照片失败: $e');
      return [];
    }
  }
}
