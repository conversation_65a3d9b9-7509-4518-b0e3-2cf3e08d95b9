import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:pocketbase/pocketbase.dart';

import '../config/pocketbase_config.dart';
import '../models/group_chat.dart';
import '../models/group_message.dart';
import '../models/user.dart';
import 'service_locator.dart';

/// 群聊服务
class GroupChatService {
  final PocketBase _pb = PocketBaseConfig.instance.client;

  /// 获取当前用户
  User? get currentUser => Services.auth.currentUser;

  /// 根据活动ID获取群聊
  Future<GroupChat?> getGroupChatByActivityId(String activityId) async {
    try {
      final records = await _pb.collection('activity_chat_groups').getList(
        filter: 'activity_id = "$activityId"',
        expand: 'creator_id',
      );

      if (records.items.isNotEmpty) {
        return GroupChat.fromJson(records.items.first.toJson());
      }
      return null;
    } catch (e) {
      debugPrint('❌ [群聊服务] 获取群聊失败: $e');
      return null;
    }
  }

  /// 获取群聊消息列表
  Future<List<GroupMessage>> getGroupMessages({
    required String groupId,
    int page = 1,
    int perPage = 50,
  }) async {
    try {
      final records = await _pb.collection('group_messages').getList(
        page: page,
        perPage: perPage,
        filter: 'group_id = "$groupId"',
        sort: '-created',
        expand: 'sender_id',
      );

      final messages = <GroupMessage>[];
      for (final record in records.items) {
        try {
          final message = GroupMessage.fromJson(record.toJson());
          messages.add(message);
        } catch (e) {
          debugPrint('❌ [群聊服务] 解析消息失败: $e');
        }
      }

      // 按时间正序排列（最新的在最后）
      messages.sort((a, b) => a.createdAt.compareTo(b.createdAt));
      return messages;
    } catch (e) {
      debugPrint('❌ [群聊服务] 获取群聊消息失败: $e');
      return [];
    }
  }

  /// 发送群聊消息
  Future<GroupMessage?> sendMessage({
    required String groupId,
    required String content,
    GroupMessageType messageType = GroupMessageType.text,
    String? replyToMessageId,
    List<String> mentionedUsers = const [],
  }) async {
    final user = currentUser;
    if (user == null) {
      debugPrint('❌ [群聊服务] 用户未登录');
      return null;
    }

    try {
      final messageData = {
        'group_id': groupId,
        'sender_id': user.id,
        'content': content,
        'message_type': messageType.name,
        'status': 'sent',
        'is_system_message': false,
        'reply_to_message_id': replyToMessageId,
        'mentioned_users': mentionedUsers,
      };

      final record = await _pb.collection('group_messages').create(body: messageData);
      
      // 更新群聊的最后消息信息
      await _updateLastMessage(groupId, content);

      debugPrint('✅ [群聊服务] 消息发送成功: ${record.id}');
      
      // 返回完整的消息对象
      final fullRecord = await _pb.collection('group_messages').getOne(
        record.id,
        expand: 'sender_id',
      );
      
      return GroupMessage.fromJson(fullRecord.toJson());
    } catch (e) {
      debugPrint('❌ [群聊服务] 发送消息失败: $e');
      return null;
    }
  }

  /// 发送系统消息
  Future<GroupMessage?> sendSystemMessage({
    required String groupId,
    required String content,
    GroupMessageType messageType = GroupMessageType.system,
  }) async {
    final user = currentUser;
    if (user == null) return null;

    try {
      final messageData = {
        'group_id': groupId,
        'sender_id': user.id,
        'content': content,
        'message_type': messageType.name,
        'status': 'sent',
        'is_system_message': true,
      };

      final record = await _pb.collection('group_messages').create(body: messageData);
      
      // 更新群聊的最后消息信息
      await _updateLastMessage(groupId, content);

      debugPrint('✅ [群聊服务] 系统消息发送成功: ${record.id}');
      
      return GroupMessage.fromJson(record.toJson());
    } catch (e) {
      debugPrint('❌ [群聊服务] 发送系统消息失败: $e');
      return null;
    }
  }

  /// 更新群聊的最后消息信息
  Future<void> _updateLastMessage(String groupId, String content) async {
    try {
      await _pb.collection('activity_chat_groups').update(
        groupId,
        body: {
          'last_message': content,
          'last_message_time': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('❌ [群聊服务] 更新最后消息失败: $e');
    }
  }

  /// 获取用户参与的群聊列表
  Future<List<GroupChat>> getUserGroupChats(String userId) async {
    try {
      // 通过活动参与关系获取用户的群聊
      final participantRecords = await _pb.collection('activity_participants').getList(
        filter: 'user_id = "$userId" && status = "joined"',
      );

      if (participantRecords.items.isEmpty) return [];

      final activityIds = participantRecords.items
          .map((record) => record.data['activity_id'] as String)
          .toList();

      // 获取对应的群聊
      final filter = 'activity_id = "${activityIds.join('" || activity_id = "')}"';
      final groupRecords = await _pb.collection('activity_chat_groups').getList(
        filter: filter,
        sort: '-last_message_time',
      );

      final groups = <GroupChat>[];
      for (final record in groupRecords.items) {
        try {
          final group = GroupChat.fromJson(record.toJson());
          groups.add(group);
        } catch (e) {
          debugPrint('❌ [群聊服务] 解析群聊失败: $e');
        }
      }

      return groups;
    } catch (e) {
      debugPrint('❌ [群聊服务] 获取用户群聊失败: $e');
      return [];
    }
  }

  /// 标记消息为已读
  Future<void> markMessagesAsRead(String groupId) async {
    // TODO: 实现消息已读状态管理
    debugPrint('📖 [群聊服务] 标记群聊消息为已读: $groupId');
  }

  /// 获取未读消息数量
  Future<int> getUnreadMessageCount(String groupId) async {
    // TODO: 实现未读消息计数
    return 0;
  }

  /// 监听群聊消息更新
  Stream<List<GroupMessage>> watchGroupMessages(String groupId) {
    // TODO: 实现实时消息监听
    // 可以使用 PocketBase 的实时订阅功能
    return Stream.empty();
  }

  /// 离开群聊
  Future<bool> leaveGroup(String groupId) async {
    final user = currentUser;
    if (user == null) return false;

    try {
      // 发送离开群聊的系统消息
      await sendSystemMessage(
        groupId: groupId,
        content: '${user.username} 离开了群聊',
        messageType: GroupMessageType.leave,
      );

      // 更新群聊成员数
      await _pb.collection('activity_chat_groups').update(
        groupId,
        body: {'member_count-': 1},
      );

      debugPrint('✅ [群聊服务] 离开群聊成功');
      return true;
    } catch (e) {
      debugPrint('❌ [群聊服务] 离开群聊失败: $e');
      return false;
    }
  }

  /// 获取群聊详情
  Future<GroupChat?> getGroupChatById(String groupId) async {
    try {
      final record = await _pb.collection('activity_chat_groups').getOne(
        groupId,
        expand: 'creator_id,activity_id',
      );

      return GroupChat.fromJson(record.toJson());
    } catch (e) {
      debugPrint('❌ [群聊服务] 获取群聊详情失败: $e');
      return null;
    }
  }
}
