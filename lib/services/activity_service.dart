import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:latlong2/latlong.dart';
import 'package:pocketbase/pocketbase.dart';

import '../config/pocketbase_config.dart';
import '../models/fishing_activity.dart';
import '../models/activity_participant.dart';
import '../models/group_chat.dart';
import '../models/user.dart';
import 'service_locator.dart';
import 'notification_service.dart';

/// 活动管理服务
class ActivityService {
  final PocketBase _pb = PocketBaseConfig.instance.client;

  /// 获取当前用户
  User? get currentUser => Services.auth.currentUser;

  /// 创建新活动
  Future<FishingActivity?> createActivity({
    required String title,
    required String description,
    required LatLng location,
    String? locationName,
    required DateTime startTime,
    required DateTime endTime,
    List<String> images = const [],
  }) async {
    final user = currentUser;
    if (user == null) {
      debugPrint('❌ [活动服务] 用户未登录');
      return null;
    }

    try {
      // 创建活动记录
      final activityData = {
        'title': title,
        'description': description,
        'location': {'lat': location.latitude, 'lng': location.longitude},
        'location_name': locationName,
        'start_time': startTime.toIso8601String(),
        'end_time': endTime.toIso8601String(),
        'max_participants': 999, // 默认最大参与人数（实际无限制）
        'current_participants': 1, // 创建者自动参与
        'creator_id': user.id,
        'status': 'active',
        'images': images,
      };

      final record = await _pb
          .collection('fishing_activities')
          .create(body: activityData);

      // 创建群聊
      final groupChat = await _createGroupChat(record.id, title, user.id);

      // 更新活动记录，关联群聊
      if (groupChat != null) {
        await _pb
            .collection('fishing_activities')
            .update(record.id, body: {'group_chat_id': groupChat.id});
      }

      // 创建者自动参与活动
      await _addParticipant(record.id, user.id);

      debugPrint('✅ [活动服务] 活动创建成功: ${record.id}');

      // 返回完整的活动对象
      return await getActivityById(record.id);
    } catch (e) {
      debugPrint('❌ [活动服务] 创建活动失败: $e');
      return null;
    }
  }

  /// 创建活动群聊
  Future<GroupChat?> _createGroupChat(
    String activityId,
    String activityTitle,
    String creatorId,
  ) async {
    try {
      final groupData = {
        'activity_id': activityId,
        'group_name': '$activityTitle - 群聊',
        'creator_id': creatorId,
        'status': 'active',
        'member_count': 1,
      };

      final record = await _pb
          .collection('group_chats')
          .create(body: groupData);
      debugPrint('✅ [活动服务] 群聊创建成功: ${record.id}');

      return GroupChat.fromJson(record.toJson());
    } catch (e) {
      debugPrint('❌ [活动服务] 创建群聊失败: $e');
      return null;
    }
  }

  /// 添加参与者
  Future<bool> _addParticipant(String activityId, String userId) async {
    try {
      final participantData = {
        'activity_id': activityId,
        'user_id': userId,
        'join_time': DateTime.now().toIso8601String(),
        'status': 'joined',
      };

      await _pb
          .collection('activity_participants')
          .create(body: participantData);
      debugPrint('✅ [活动服务] 参与者添加成功: $userId');
      return true;
    } catch (e) {
      debugPrint('❌ [活动服务] 添加参与者失败: $e');
      return false;
    }
  }

  /// 根据ID获取活动详情
  Future<FishingActivity?> getActivityById(String activityId) async {
    try {
      final record = await _pb
          .collection('fishing_activities')
          .getOne(activityId, expand: 'creator_id');

      return FishingActivity.fromJson(record.toJson());
    } catch (e) {
      debugPrint('❌ [活动服务] 获取活动详情失败: $e');
      return null;
    }
  }

  /// 获取附近的活动
  Future<List<FishingActivity>> getNearbyActivities({
    required LatLng center,
    double radiusKm = 10.0,
    int perPage = 20,
    int page = 1,
  }) async {
    try {
      // 计算边界框
      const double kmPerDegree = 111.0;
      final latRange = radiusKm / kmPerDegree;
      final lngRange =
          radiusKm / (kmPerDegree * math.cos(center.latitude * math.pi / 180));

      final minLat = center.latitude - latRange;
      final maxLat = center.latitude + latRange;
      final minLng = center.longitude - lngRange;
      final maxLng = center.longitude + lngRange;

      // 构建过滤条件
      final filter =
          'status = "active" && '
          'location.lat >= $minLat && location.lat <= $maxLat && '
          'location.lng >= $minLng && location.lng <= $maxLng';

      final records = await _pb
          .collection('fishing_activities')
          .getList(
            page: page,
            perPage: perPage,
            filter: filter,
            sort: '-created',
            expand: 'creator_id',
          );

      final activities = <FishingActivity>[];
      for (final record in records.items) {
        try {
          final activity = FishingActivity.fromJson(record.toJson());
          activities.add(activity);
        } catch (e) {
          debugPrint('❌ [活动服务] 解析活动失败: $e');
        }
      }

      debugPrint('✅ [活动服务] 获取到 ${activities.length} 个附近活动');
      return activities;
    } catch (e) {
      debugPrint('❌ [活动服务] 获取附近活动失败: $e');
      return [];
    }
  }

  /// 参加活动
  Future<bool> joinActivity(String activityId) async {
    final user = currentUser;
    if (user == null) return false;

    try {
      // 检查是否已经参与
      final existing = await _pb
          .collection('activity_participants')
          .getList(
            filter:
                'activity_id = "$activityId" && user_id = "${user.id}" && status = "joined"',
          );

      if (existing.items.isNotEmpty) {
        debugPrint('⚠️ [活动服务] 用户已参与该活动');
        return true;
      }

      // 检查活动是否存在且未满员
      final activity = await getActivityById(activityId);
      if (activity == null) {
        debugPrint('❌ [活动服务] 活动不存在');
        return false;
      }

      if (activity.isFull) {
        debugPrint('❌ [活动服务] 活动已满员');
        return false;
      }

      // 添加参与者
      await _addParticipant(activityId, user.id);

      // 更新活动参与人数
      await _pb
          .collection('fishing_activities')
          .update(activityId, body: {'current_participants+': 1});

      // 如果有群聊，更新群聊成员数
      if (activity.groupChatId != null) {
        await _pb
            .collection('group_chats')
            .update(activity.groupChatId!, body: {'member_count+': 1});
      }

      // 发送参加确认通知给用户
      await Services.notification.createActivityNotification(
        userId: user.id,
        activityId: activityId,
        activityTitle: activity.title,
        activityType: ActivityNotificationType.joinConfirm,
      );

      // 通知活动创建者有新成员加入
      if (activity.creatorId != user.id) {
        await Services.notification.createActivityNotification(
          userId: activity.creatorId,
          activityId: activityId,
          activityTitle: activity.title,
          activityType: ActivityNotificationType.newParticipant,
        );
      }

      debugPrint('✅ [活动服务] 参加活动成功');
      return true;
    } catch (e) {
      debugPrint('❌ [活动服务] 参加活动失败: $e');
      return false;
    }
  }

  /// 退出活动
  Future<bool> leaveActivity(String activityId) async {
    final user = currentUser;
    if (user == null) return false;

    try {
      // 查找参与记录
      final participants = await _pb
          .collection('activity_participants')
          .getList(
            filter:
                'activity_id = "$activityId" && user_id = "${user.id}" && status = "joined"',
          );

      if (participants.items.isEmpty) {
        debugPrint('⚠️ [活动服务] 用户未参与该活动');
        return true;
      }

      // 更新参与状态为已退出
      await _pb
          .collection('activity_participants')
          .update(participants.items.first.id, body: {'status': 'left'});

      // 更新活动参与人数
      await _pb
          .collection('fishing_activities')
          .update(activityId, body: {'current_participants-': 1});

      // 获取活动信息更新群聊成员数
      final activity = await getActivityById(activityId);
      if (activity?.groupChatId != null) {
        await _pb
            .collection('group_chats')
            .update(activity!.groupChatId!, body: {'member_count-': 1});
      }

      debugPrint('✅ [活动服务] 退出活动成功');
      return true;
    } catch (e) {
      debugPrint('❌ [活动服务] 退出活动失败: $e');
      return false;
    }
  }

  /// 获取活动参与者列表
  Future<List<ActivityParticipant>> getActivityParticipants(
    String activityId,
  ) async {
    try {
      final records = await _pb
          .collection('activity_participants')
          .getList(
            filter: 'activity_id = "$activityId" && status = "joined"',
            sort: 'join_time',
            expand: 'user_id',
          );

      final participants = <ActivityParticipant>[];
      for (final record in records.items) {
        try {
          final participant = ActivityParticipant.fromJson(record.toJson());
          participants.add(participant);
        } catch (e) {
          debugPrint('❌ [活动服务] 解析参与者失败: $e');
        }
      }

      return participants;
    } catch (e) {
      debugPrint('❌ [活动服务] 获取参与者列表失败: $e');
      return [];
    }
  }

  /// 检查用户是否参与了某个活动
  Future<bool> isUserParticipant(String activityId, String userId) async {
    try {
      final records = await _pb
          .collection('activity_participants')
          .getList(
            filter:
                'activity_id = "$activityId" && user_id = "$userId" && status = "joined"',
          );

      return records.items.isNotEmpty;
    } catch (e) {
      debugPrint('❌ [活动服务] 检查参与状态失败: $e');
      return false;
    }
  }

  /// 获取用户参与的活动列表
  Future<List<FishingActivity>> getUserActivities(String userId) async {
    try {
      // 先获取用户参与的活动ID列表
      final participantRecords = await _pb
          .collection('activity_participants')
          .getList(
            filter: 'user_id = "$userId" && status = "joined"',
            sort: '-join_time',
          );

      if (participantRecords.items.isEmpty) return [];

      final activityIds =
          participantRecords.items
              .map((record) => record.data['activity_id'] as String)
              .toList();

      // 获取活动详情
      final filter = 'id = "${activityIds.join('" || id = "')}"';
      final activityRecords = await _pb
          .collection('fishing_activities')
          .getList(filter: filter, expand: 'creator_id');

      final activities = <FishingActivity>[];
      for (final record in activityRecords.items) {
        try {
          final activity = FishingActivity.fromJson(record.toJson());
          activities.add(activity);
        } catch (e) {
          debugPrint('❌ [活动服务] 解析活动失败: $e');
        }
      }

      return activities;
    } catch (e) {
      debugPrint('❌ [活动服务] 获取用户活动失败: $e');
      return [];
    }
  }
}
