import 'dart:async';
import 'package:flutter/foundation.dart';
import '../config/pocketbase_config.dart';
import '../pages/message/notification_page.dart';
import '../models/user.dart';
import 'auth_service_new.dart';

/// 通知服务
///
/// 负责系统通知的获取、标记已读、删除等功能
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final AuthService _authService = AuthService();
  final _pb = PocketBaseConfig.instance.client;

  // 通知缓存
  List<SystemNotification> _notificationsCache = [];

  // 状态通知
  final ValueNotifier<List<SystemNotification>> _notificationsNotifier =
      ValueNotifier<List<SystemNotification>>([]);
  final ValueNotifier<int> _unreadCountNotifier = ValueNotifier<int>(0);

  /// 获取当前用户
  User? get currentUser => _authService.currentUser;

  /// 通知列表通知器
  ValueNotifier<List<SystemNotification>> get notificationsNotifier =>
      _notificationsNotifier;

  /// 未读通知数通知器
  ValueNotifier<int> get unreadCountNotifier => _unreadCountNotifier;

  /// 获取系统通知列表
  Future<List<SystemNotification>> getNotifications() async {
    final user = currentUser;
    if (user == null) return [];

    try {
      final records = await _pb
          .collection('notifications')
          .getList(filter: 'user = "${user.id}"', sort: '-created', perPage: 50)
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              debugPrint('⏰ [通知服务] 请求超时，使用缓存数据');
              throw TimeoutException('获取通知列表超时', const Duration(seconds: 10));
            },
          );

      final notifications = <SystemNotification>[];

      for (final record in records.items) {
        try {
          final notification = _parseNotification(record);
          if (notification != null) {
            notifications.add(notification);
          }
        } catch (e) {
          debugPrint('❌ [通知服务] 解析通知失败: $e');
        }
      }

      _notificationsCache = notifications;
      _notificationsNotifier.value = notifications;
      _updateUnreadCount();

      debugPrint('✅ [通知服务] 获取到 ${notifications.length} 条通知');
      return notifications;
    } catch (e) {
      debugPrint('❌ [通知服务] 获取通知列表失败: $e');

      // 如果是超时错误且有缓存数据，返回缓存
      if (e is TimeoutException && _notificationsCache.isNotEmpty) {
        debugPrint('🔄 [通知服务] 网络超时，返回缓存数据 (${_notificationsCache.length} 条)');
        return _notificationsCache;
      }

      // 其他错误返回空列表
      return [];
    }
  }

  /// 获取未读通知数量
  Future<int> getUnreadCount() async {
    final user = currentUser;
    if (user == null) return 0;

    try {
      final result = await _pb
          .collection('notifications')
          .getList(filter: 'user = "${user.id}" && is_read = false', perPage: 1)
          .timeout(
            const Duration(seconds: 8),
            onTimeout: () {
              debugPrint('⏰ [通知服务] 获取未读数超时');
              throw TimeoutException('获取未读数超时', const Duration(seconds: 8));
            },
          );

      final count = result.totalItems;
      _unreadCountNotifier.value = count;

      debugPrint('✅ [通知服务] 未读通知数: $count');
      return count;
    } catch (e) {
      debugPrint('❌ [通知服务] 获取未读数量失败: $e');

      // 如果是超时错误，返回当前缓存的未读数
      if (e is TimeoutException) {
        final cachedUnreadCount =
            _notificationsCache.where((n) => !n.isRead).length;
        debugPrint('🔄 [通知服务] 网络超时，返回缓存未读数: $cachedUnreadCount');
        return cachedUnreadCount;
      }

      return 0;
    }
  }

  /// 标记通知为已读
  Future<void> markAsRead(String notificationId) async {
    try {
      await _pb
          .collection('notifications')
          .update(notificationId, body: {'is_read': true});

      // 更新本地缓存
      final index = _notificationsCache.indexWhere(
        (n) => n.id == notificationId,
      );
      if (index != -1) {
        _notificationsCache[index] = _notificationsCache[index].copyWith(
          isRead: true,
        );
        _notificationsNotifier.value = List.from(_notificationsCache);
        _updateUnreadCount();
      }

      debugPrint('✅ [通知服务] 标记通知为已读: $notificationId');
    } catch (e) {
      debugPrint('❌ [通知服务] 标记已读失败: $e');
    }
  }

  /// 标记所有通知为已读
  Future<void> markAllAsRead() async {
    final user = currentUser;
    if (user == null) return;

    try {
      // 获取所有未读通知
      final unreadRecords = await _pb
          .collection('notifications')
          .getList(
            filter: 'user = "${user.id}" && is_read = false',
            perPage: 100,
          );

      // 批量标记为已读
      for (final record in unreadRecords.items) {
        await _pb
            .collection('notifications')
            .update(record.id, body: {'is_read': true});
      }

      // 更新本地缓存
      for (int i = 0; i < _notificationsCache.length; i++) {
        if (!_notificationsCache[i].isRead) {
          _notificationsCache[i] = _notificationsCache[i].copyWith(
            isRead: true,
          );
        }
      }

      _notificationsNotifier.value = List.from(_notificationsCache);
      _updateUnreadCount();

      debugPrint('✅ [通知服务] 标记所有通知为已读');
    } catch (e) {
      debugPrint('❌ [通知服务] 标记所有已读失败: $e');
    }
  }

  /// 删除通知
  Future<void> deleteNotification(String notificationId) async {
    try {
      await _pb.collection('notifications').delete(notificationId);

      // 从本地缓存移除
      _notificationsCache.removeWhere((n) => n.id == notificationId);
      _notificationsNotifier.value = List.from(_notificationsCache);
      _updateUnreadCount();

      debugPrint('✅ [通知服务] 删除通知: $notificationId');
    } catch (e) {
      debugPrint('❌ [通知服务] 删除通知失败: $e');
    }
  }

  /// 创建新通知
  Future<void> createNotification({
    required String userId,
    required NotificationType type,
    required String title,
    required String content,
    String? relatedId,
  }) async {
    try {
      await _pb
          .collection('notifications')
          .create(
            body: {
              'user': userId,
              'type': type.name,
              'title': title,
              'content': content,
              'is_read': false,
              'related_id': relatedId,
            },
          );

      debugPrint('✅ [通知服务] 创建通知成功: $title');
    } catch (e) {
      debugPrint('❌ [通知服务] 创建通知失败: $e');
    }
  }

  /// 更新未读数量
  void _updateUnreadCount() {
    final unreadCount = _notificationsCache.where((n) => !n.isRead).length;
    _unreadCountNotifier.value = unreadCount;
  }

  /// 解析通知记录
  SystemNotification? _parseNotification(dynamic record) {
    try {
      return SystemNotification(
        id: record.id,
        type: _parseNotificationType(record.data['type']),
        title: record.data['title'] ?? '',
        content: record.data['content'] ?? '',
        createdAt: DateTime.parse(record.created),
        isRead: record.data['is_read'] ?? false,
      );
    } catch (e) {
      debugPrint('❌ [通知服务] 解析通知记录失败: $e');
      return null;
    }
  }

  /// 解析通知类型
  NotificationType _parseNotificationType(String? typeStr) {
    switch (typeStr) {
      case 'like':
        return NotificationType.like;
      case 'follow':
        return NotificationType.follow;
      case 'comment':
        return NotificationType.comment;
      case 'update':
        return NotificationType.update;
      default:
        return NotificationType.system;
    }
  }

  /// 重试获取通知（用于网络恢复后的手动刷新）
  Future<List<SystemNotification>> retryGetNotifications() async {
    debugPrint('🔄 [通知服务] 手动重试获取通知');
    return await getNotifications();
  }

  /// 检查网络连接并智能获取通知
  Future<List<SystemNotification>> getNotificationsWithFallback() async {
    try {
      return await getNotifications();
    } catch (e) {
      // 如果获取失败，等待2秒后重试一次
      debugPrint('🔄 [通知服务] 首次获取失败，2秒后重试');
      await Future.delayed(const Duration(seconds: 2));

      try {
        return await getNotifications();
      } catch (retryError) {
        debugPrint('❌ [通知服务] 重试也失败，返回缓存数据');
        return _notificationsCache;
      }
    }
  }

  /// 清除缓存
  void clearCache() {
    _notificationsCache.clear();
    _notificationsNotifier.value = [];
    _unreadCountNotifier.value = 0;
  }

  /// 创建活动相关通知
  Future<void> createActivityNotification({
    required String userId,
    required String activityId,
    required String activityTitle,
    required ActivityNotificationType activityType,
  }) async {
    String title;
    String content;
    NotificationType type;

    switch (activityType) {
      case ActivityNotificationType.newActivity:
        title = '新的钓鱼活动';
        content = '附近有新的钓鱼活动：$activityTitle';
        type = NotificationType.activity;
        break;
      case ActivityNotificationType.joinConfirm:
        title = '参加活动成功';
        content = '您已成功参加活动：$activityTitle';
        type = NotificationType.activity;
        break;
      case ActivityNotificationType.activityReminder:
        title = '活动提醒';
        content = '您参加的活动 "$activityTitle" 即将开始';
        type = NotificationType.activity;
        break;
      case ActivityNotificationType.activityStart:
        title = '活动开始';
        content = '您参加的活动 "$activityTitle" 已经开始了';
        type = NotificationType.activity;
        break;
      case ActivityNotificationType.activityEnd:
        title = '活动结束';
        content = '活动 "$activityTitle" 已经结束，快来分享您的收获吧';
        type = NotificationType.activity;
        break;
      case ActivityNotificationType.newParticipant:
        title = '新成员加入';
        content = '有新成员加入了您的活动：$activityTitle';
        type = NotificationType.activity;
        break;
    }

    await createNotification(
      userId: userId,
      type: type,
      title: title,
      content: content,
      relatedId: activityId,
    );
  }

  /// 批量发送活动通知给参与者
  Future<void> notifyActivityParticipants({
    required List<String> participantIds,
    required String activityId,
    required String activityTitle,
    required ActivityNotificationType activityType,
  }) async {
    for (final userId in participantIds) {
      await createActivityNotification(
        userId: userId,
        activityId: activityId,
        activityTitle: activityTitle,
        activityType: activityType,
      );
    }
  }

  /// 销毁服务
  void dispose() {
    _notificationsNotifier.dispose();
    _unreadCountNotifier.dispose();
  }
}

/// 活动通知类型
enum ActivityNotificationType {
  newActivity, // 新活动推荐
  joinConfirm, // 参加确认
  activityReminder, // 活动提醒
  activityStart, // 活动开始
  activityEnd, // 活动结束
  newParticipant, // 新成员加入
}
