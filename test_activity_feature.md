# "一起钓鱼"功能测试指南

## 功能概述

本次实现了完整的"一起钓鱼"功能，包括：

1. ✅ **活动创建** - 用户可以发起钓鱼活动
2. ✅ **地图标记** - 活动在地图上显示标记
3. ✅ **活动展示** - 在探索页面推荐活动
4. ✅ **参与管理** - 用户可以参加/退出活动
5. ✅ **群聊功能** - 参与者自动加入活动群聊
6. ✅ **通知系统** - 活动相关通知推送
7. ✅ **添加按钮** - 统一的添加选择界面

## 测试步骤

### 1. 基础功能测试

#### 1.1 添加按钮选择界面
- [ ] 点击主页底部的添加按钮
- [ ] 验证弹出选择界面（发布钓点 vs 一起钓鱼）
- [ ] 点击"一起钓鱼"选项

#### 1.2 活动创建
- [ ] 填写活动标题（必填）
- [ ] 填写活动描述（必填）
- [ ] 填写活动地点（必填）
- [ ] 选择开始时间和结束时间
- [ ] 调整最大参与人数（默认99）
- [ ] 点击"发布"按钮
- [ ] 验证活动创建成功提示

### 2. 地图标记测试

#### 2.1 活动标记显示
- [ ] 返回主页地图
- [ ] 验证新创建的活动在地图上显示绿色圆形标记
- [ ] 验证活动标记显示活动照片（如有）或发起人头像
- [ ] 点击活动标记，验证显示活动基本信息

#### 2.2 混合标记显示
- [ ] 验证地图同时显示钓点标记和活动标记
- [ ] 验证两种标记样式区分明显（钓点vs活动）

### 3. 探索页面测试

#### 3.1 活动推荐
- [ ] 进入探索页面
- [ ] 点击"一起钓鱼"标签
- [ ] 验证显示附近的活动列表
- [ ] 验证活动卡片显示完整信息：
  - 活动标题和描述
  - 发起人信息
  - 时间和地点
  - 参与人数
  - 活动状态

#### 3.2 活动参与
- [ ] 点击活动卡片的"参与"按钮
- [ ] 验证参与成功提示
- [ ] 验证参与人数更新
- [ ] 验证按钮状态变化

### 4. 活动详情页面测试

#### 4.1 详情展示
- [ ] 点击活动卡片进入详情页面
- [ ] 验证显示完整活动信息：
  - 活动标题和描述
  - 发起人信息和头像
  - 活动状态标签
  - 时间、地点、参与人数
  - 参与者头像列表

#### 4.2 参与管理
- [ ] 测试"参加活动"按钮功能
- [ ] 测试"退出活动"按钮功能
- [ ] 验证参与状态实时更新

### 5. 群聊功能测试

#### 5.1 群聊创建
- [ ] 验证活动创建时自动创建群聊
- [ ] 验证参与者自动加入群聊

#### 5.2 群聊功能
- [ ] 点击"进入群聊"按钮
- [ ] 验证群聊页面正常显示
- [ ] 测试发送消息功能
- [ ] 验证消息正常显示
- [ ] 测试退出群聊功能

### 6. 通知系统测试

#### 6.1 参与通知
- [ ] 参加活动后验证收到确认通知
- [ ] 活动创建者验证收到新成员通知

#### 6.2 通知显示
- [ ] 进入通知页面
- [ ] 验证活动通知正常显示
- [ ] 验证通知图标和颜色正确

## 已知问题和限制

### 当前限制
1. **实时消息** - 群聊消息暂未实现实时推送
2. **消息已读** - 未实现消息已读状态管理
3. **活动提醒** - 未实现定时提醒功能
4. **活动分享** - 分享功能待实现

### 性能考虑
1. **地图加载** - 大量活动时可能影响地图性能
2. **消息加载** - 群聊消息分页加载已实现
3. **通知推送** - 批量通知可能需要优化

## 后续优化建议

### 短期优化
1. 实现WebSocket实时消息推送
2. 添加活动搜索和筛选功能
3. 优化地图标记性能
4. 完善错误处理和用户反馈

### 长期规划
1. 活动评价和评分系统
2. 活动推荐算法优化
3. 社交分享功能
4. 活动数据统计和分析

## 数据库集合验证

确保以下PocketBase集合已正确创建：

### fishing_activities
- [x] title (text, required)
- [x] description (text)
- [x] location (geoPoint)
- [x] location_name (text)
- [x] start_time (date, required)
- [x] end_time (date, required)
- [x] max_participants (number, 2-99, default: 99)
- [x] current_participants (number, default: 1)
- [x] creator_id (relation to users)
- [x] status (select: active, cancelled, completed)
- [x] images (json)
- [x] group_chat_id (relation to activity_chat_groups)

### activity_participants
- [x] activity_id (relation to fishing_activities)
- [x] user_id (relation to users)
- [x] join_time (date)
- [x] status (select: joined, left, kicked)

### activity_chat_groups
- [x] activity_id (relation to fishing_activities)
- [x] group_name (text)
- [x] description (text)
- [x] avatar (text)
- [x] creator_id (relation to users)
- [x] status (select: active, archived, deleted)
- [x] member_count (number, default: 0)
- [x] last_message (text)
- [x] last_message_time (date)

### group_messages
- [x] group_id (relation to activity_chat_groups)
- [x] sender_id (relation to users)
- [x] content (text)
- [x] message_type (select: text, image, system, join, leave)
- [x] status (text, default: sent)
- [x] is_system_message (bool, default: false)
- [x] reply_to_message_id (text)
- [x] mentioned_users (json)

## 测试完成确认

- [ ] 所有基础功能测试通过
- [ ] 地图标记功能正常
- [ ] 探索页面集成成功
- [ ] 活动详情页面完整
- [ ] 群聊功能基本可用
- [ ] 通知系统正常工作
- [ ] 数据库集合结构正确
- [ ] 无严重错误或崩溃

测试完成后，"一起钓鱼"功能即可投入使用！
